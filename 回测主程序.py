"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import warnings
import pandas as pd

from core.backtest import run_backtest
from core.model.backtest_config import load_config
from core.version import version_prompt

# ====================================================================================================
# ** 配置与初始化 **
# 设定警告过滤和数据展示选项，以优化控制台输出的阅读体验
# ====================================================================================================
warnings.filterwarnings('ignore')  # 忽略警告信息，保持输出简洁
pd.set_option('expand_frame_repr', False)  # 设置数据框显示不换行
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)

def load_all_strategies():
    import importlib
    all_strageties = """
    筹码基本面.py
    筹码小市值_下方筹码集中度.py
    筹码小市值_中期筹码变动.py
    筹码小市值_主力筹码控盘率.py
    筹码小市值_短期筹码变动.py
    筹码小市值_筹码偏离度.py
    筹码小市值_筹码成本区间.py
    筹码小市值_筹码波动率.py
    筹码小市值_筹码穿透率.py
    筹码小市值_筹码获利比例.py
    筹码小市值_筹码集中度.py
    筹码小市值_胜率.py
    筹码小市值_长期筹码变动.py
    """

    strategies_list = [x[:-3] for x in all_strageties.split() if '#' not in x]
    # print(all_strageties.split())
    strategy_list = {}
    for stg in strategies_list:
        strategy_module = importlib.import_module('实盘.' + stg)
        strategy_list[stg] = strategy_module.strategy_list

    return strategy_list

if __name__ == '__main__':
    # ====================================================================================================
    # 0. 准备配置
    # ====================================================================================================
    # 启动回测系统
    # version_prompt()
    print('🌀 回测系统启动中，请稍候...')

    # 加载回测配置
    conf = load_config()
    for name, stg_list in load_all_strategies().items():
        conf.name = name
        # print(type(stg_list))
        conf.strategy_list = stg_list
        print(conf.info())

        # ====================================================================================================
        # 1. 开始回测
        # ====================================================================================================
        run_backtest(conf, del_cache= conf.del_cache)
