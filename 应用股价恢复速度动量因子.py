#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股价恢复速度动量因子应用示例

本脚本展示如何应用和测试股价恢复速度动量因子
包括：
1. 因子计算配置
2. 单因子分析
3. 参数优化
4. 因子验证
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def test_factor_calculation():
    """测试因子计算功能"""
    print("=" * 80)
    print("🧪 测试股价恢复速度动量因子计算")
    print("=" * 80)
    
    # 创建模拟数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    
    # 模拟股票数据
    data = {
        '交易日期': dates,
        '收盘价': 10 + np.cumsum(np.random.randn(len(dates)) * 0.02),
        '跌停价': None,  # 将在下面计算
        '收盘价_复权': None,  # 将在下面计算
    }
    
    df = pd.DataFrame(data)
    df['收盘价_复权'] = df['收盘价']  # 简化处理
    df['跌停价'] = df['收盘价'] * 0.9  # 假设跌停价为收盘价的90%
    
    # 人工创建几个跌停事件
    limit_down_indices = [50, 150, 250]
    for idx in limit_down_indices:
        if idx < len(df):
            df.loc[idx, '收盘价'] = df.loc[idx, '跌停价']
            df.loc[idx, '收盘价_复权'] = df.loc[idx, '跌停价']
    
    print(f"📊 创建了 {len(df)} 天的模拟数据")
    print(f"🔻 设置了 {len(limit_down_indices)} 个跌停事件")
    
    # 导入因子计算函数
    try:
        import sys
        sys.path.append('.')
        from 因子库.跌停起步改 import add_factor
        
        # 测试不同方法的因子计算
        methods = [
            (1, 20, 1.05, "恢复天数动量"),
            (2, 20, 1.05, "恢复幅度动量"), 
            (3, 20, 1.05, "恢复速度动量"),
            (4, 20, 1.05, "综合动量评分")
        ]
        
        results = {}
        for method, lookback, threshold, name in methods:
            print(f"\n🔬 测试方法{method}: {name}")
            
            param = (method, lookback, threshold)
            col_name = f'动量因子_方法{method}'
            
            try:
                factor_result = add_factor(
                    df.copy(), 
                    param=param, 
                    col_name=col_name
                )
                
                factor_values = factor_result[col_name]
                non_zero_count = (factor_values != 0).sum()
                max_value = factor_values.max()
                min_value = factor_values.min()
                
                print(f"  ✅ 计算成功")
                print(f"  📈 非零值数量: {non_zero_count}")
                print(f"  📊 数值范围: [{min_value:.4f}, {max_value:.4f}]")
                
                results[name] = factor_values
                
            except Exception as e:
                print(f"  ❌ 计算失败: {e}")
        
        # 显示结果统计
        if results:
            print(f"\n📋 因子计算结果汇总:")
            for name, values in results.items():
                print(f"  {name}: 均值={values.mean():.4f}, 标准差={values.std():.4f}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入因子文件失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False


def create_factor_config_example():
    """创建因子配置示例"""
    print("\n" + "=" * 80)
    print("⚙️ 因子配置示例")
    print("=" * 80)
    
    # 因子配置示例
    factor_configs = {
        "基础配置": {
            "因子名称": "跌停起步改",
            "参数说明": {
                "method": "计算方法 (1-4)",
                "lookback_days": "回看天数，用于寻找跌停事件",
                "recovery_threshold": "恢复阈值，如1.05表示恢复5%"
            }
        },
        
        "参数组合示例": [
            {
                "名称": "快速恢复-天数动量",
                "参数": (1, 10, 1.03),
                "说明": "10天内恢复3%的天数动量"
            },
            {
                "名称": "标准恢复-幅度动量", 
                "参数": (2, 20, 1.05),
                "说明": "20天内恢复5%的幅度动量"
            },
            {
                "名称": "长期恢复-速度动量",
                "参数": (3, 30, 1.10),
                "说明": "30天内恢复10%的速度动量"
            },
            {
                "名称": "综合动量评分",
                "参数": (4, 20, 1.05),
                "说明": "综合三种方法的动量评分"
            }
        ],
        
        "应用场景": {
            "短线交易": "使用方法1，关注恢复天数",
            "中线持有": "使用方法2，关注恢复幅度", 
            "趋势跟踪": "使用方法3，关注恢复速度",
            "综合选股": "使用方法4，综合评估"
        }
    }
    
    print("📋 因子配置结构:")
    for key, value in factor_configs.items():
        print(f"\n🔸 {key}:")
        if isinstance(value, dict):
            for sub_key, sub_value in value.items():
                print(f"  • {sub_key}: {sub_value}")
        elif isinstance(value, list):
            for i, item in enumerate(value, 1):
                print(f"  {i}. {item['名称']}")
                print(f"     参数: {item['参数']}")
                print(f"     说明: {item['说明']}")
    
    return factor_configs


def demonstrate_factor_analysis():
    """演示因子分析流程"""
    print("\n" + "=" * 80)
    print("📈 因子分析流程演示")
    print("=" * 80)
    
    analysis_steps = [
        {
            "步骤": "1. 因子验证",
            "操作": "验证因子是否存在于因子库中",
            "代码": """
from core.utils.factor_hub import FactorHub
factor_instance = FactorHub.get_by_name('跌停起步改')
print("✅ 因子验证成功")
            """
        },
        {
            "步骤": "2. 参数配置",
            "操作": "设置因子计算参数",
            "代码": """
# 配置不同的参数组合
params = [
    (1, 10, 1.03),  # 快速恢复-天数动量
    (2, 20, 1.05),  # 标准恢复-幅度动量
    (3, 30, 1.10),  # 长期恢复-速度动量
    (4, 20, 1.05),  # 综合动量评分
]
            """
        },
        {
            "步骤": "3. 因子计算",
            "操作": "批量计算不同参数下的因子值",
            "代码": """
for param in params:
    col_name = f'动量因子_{param[0]}_{param[1]}_{param[2]}'
    factor_result = factor_instance.add_factor(
        df.copy(), 
        param=param, 
        col_name=col_name
    )
            """
        },
        {
            "步骤": "4. 因子分析",
            "操作": "使用因子分析工具进行深度分析",
            "代码": """
# 使用因子分析工具
from 因子分析工具 import validate_and_analyze_factor

result = validate_and_analyze_factor(
    factor_name='跌停起步改',
    param=(1, 20, 1.05),
    period_offset='5D_0'
)

if result.get('success'):
    print(f"因子得分: {result['factor_score']:.4f}")
            """
        },
        {
            "步骤": "5. 参数优化",
            "操作": "批量测试所有参数组合，找到最优参数",
            "代码": """
from 因子分析工具 import analyze_factor_all_params

batch_result = analyze_factor_all_params('跌停起步改')
if batch_result.get('success'):
    print(f"最优参数: {batch_result['best_param']}")
    print(f"最优得分: {batch_result['best_score']:.4f}")
            """
        }
    ]
    
    for step in analysis_steps:
        print(f"\n🔸 {step['步骤']}")
        print(f"   操作: {step['操作']}")
        print(f"   代码示例:")
        print("   " + "\n   ".join(step['代码'].strip().split('\n')))


def create_practical_application():
    """创建实际应用示例"""
    print("\n" + "=" * 80)
    print("🚀 实际应用示例")
    print("=" * 80)
    
    # 创建应用脚本
    application_script = '''
"""
股价恢复速度动量因子实际应用脚本
"""

def apply_momentum_factor():
    """应用动量因子进行选股"""
    
    # 1. 加载配置
    from core.model.backtest_config import load_config
    conf = load_config()
    
    # 2. 配置因子参数
    # 在config.py中添加因子配置
    factor_params = {
        '跌停起步改': [
            (1, 10, 1.03),  # 快速恢复
            (2, 20, 1.05),  # 标准恢复  
            (3, 30, 1.10),  # 长期恢复
            (4, 20, 1.05),  # 综合评分
        ]
    }
    
    # 3. 计算因子
    from core.select_stock import calculate_factors
    calculate_factors(conf, boost=True)
    
    # 4. 因子分析
    from tools.tool1_单因子分析 import factor_analysis
    
    # 分析不同参数的因子表现
    for param in factor_params['跌停起步改']:
        factor_name = f"factor_跌停起步改_{param}"
        factor_analysis(factor_name, None, conf, [], boost=True)
    
    # 5. 选股应用
    from core.select_stock import select_stock
    
    # 使用最优参数进行选股
    best_factor = "factor_跌停起步改_(4, 20, 1.05)"  # 综合评分
    
    # 配置选股策略
    strategy_config = {
        'name': '动量恢复策略',
        'factor_name': best_factor,
        'select_num': 20,  # 选择20只股票
        'is_sort_asc': False,  # 降序排列，选择动量最强的
    }
    
    # 执行选股
    result = select_stock(strategy_config, conf)
    
    return result

if __name__ == '__main__':
    result = apply_momentum_factor()
    print("选股完成！")
'''
    
    print("📝 实际应用脚本示例:")
    print(application_script)
    
    # 保存应用脚本
    script_path = Path("应用动量因子选股.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(application_script)
    
    print(f"\n💾 应用脚本已保存到: {script_path}")


def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 80)
    print("📖 使用指南")
    print("=" * 80)
    
    guide = """
🎯 股价恢复速度动量因子使用指南

1️⃣ 基础使用
   • 因子文件: 因子库/跌停起步改.py
   • 因子名称: 跌停起步改
   • 参数格式: (method, lookback_days, recovery_threshold)

2️⃣ 参数说明
   • method: 计算方法
     - 1: 恢复天数动量 (恢复越快，评分越高)
     - 2: 恢复幅度动量 (恢复幅度越大，评分越高)
     - 3: 恢复速度动量 (恢复速度越快，评分越高)
     - 4: 综合动量评分 (综合前三种方法)
   
   • lookback_days: 回看天数 (建议10-30天)
   • recovery_threshold: 恢复阈值 (建议1.03-1.10)

3️⃣ 配置步骤
   ① 在config.py中添加因子配置
   ② 运行 program/step2_计算因子.py
   ③ 使用 因子分析工具.py 进行分析
   ④ 配置选股策略并运行

4️⃣ 推荐参数组合
   • 短线策略: (1, 10, 1.03) - 关注快速恢复
   • 中线策略: (2, 20, 1.05) - 关注恢复幅度
   • 长线策略: (3, 30, 1.10) - 关注恢复质量
   • 综合策略: (4, 20, 1.05) - 平衡各方面

5️⃣ 注意事项
   • 确保数据包含跌停价字段
   • 因子值范围限制在[-3, 3]
   • 建议与其他因子组合使用
   • 定期重新优化参数

6️⃣ 性能特点
   • 已优化计算性能，支持大数据量
   • 向量化操作，计算速度快
   • 内存使用效率高
   • 支持实时计算
"""
    
    print(guide)


def main():
    """主函数"""
    print("🎉 股价恢复速度动量因子应用指南")
    print("=" * 80)
    
    # 1. 测试因子计算
    if test_factor_calculation():
        print("✅ 因子计算测试通过")
    else:
        print("❌ 因子计算测试失败")
        return
    
    # 2. 创建配置示例
    create_factor_config_example()
    
    # 3. 演示分析流程
    demonstrate_factor_analysis()
    
    # 4. 创建实际应用
    create_practical_application()
    
    # 5. 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 80)
    print("🎊 应用指南完成！")
    print("📁 相关文件:")
    print("  • 因子文件: 因子库/跌停起步改.py")
    print("  • 应用脚本: 应用动量因子选股.py")
    print("  • 分析工具: 因子分析工具.py")
    print("=" * 80)


if __name__ == '__main__':
    main()
