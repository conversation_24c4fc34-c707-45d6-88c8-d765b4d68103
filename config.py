"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import os
from pathlib import Path

from core.utils.path_kit import get_folder_path

# ====================================================================================================
# 1️⃣ 回测配置
# ====================================================================================================
# 回测数据的起始时间。如果因子使用滚动计算方法，在回测初期因子值可能为 NaN，实际的首次交易日期可能晚于这个起始时间。
start_date = '2024-01-01'
# 回测数据的结束时间。可以设为 None，表示使用最新数据；也可以指定具体日期，例如 '2024-11-01'。
end_date = '2025-06-30'

# ====================================================================================================
# 2️⃣ 数据配置
# ====================================================================================================
data_center_path = r"D:\data"  # 数据中心的文件夹
runtime_data_path = get_folder_path('data')  # 回测结果存放的的文件夹，默认为项目文件夹下的 data 文件夹，可以自定义

# ====================================================================================================
# 3️⃣ 策略配置
# ====================================================================================================
# backtest_name = '方天画戟'  # 回测的策略组合的名称。可以自己任意取。一般建议，一个回测组，就是实盘中的一个账户。
# # 策略明细
# strategy_list = [
#     {
#         'name': '策略1',  # 策略名
#         'hold_period': '3D',  # 持仓周期，W 代表周，M 代表月，还支持日频：3D、5D、10D
#         'offset_list': [0],
#         'select_num': 10,  # 选股数量，可以是整数，也可以是小数，比如 0.1 表示选取 10% 的股票
#         'cap_weight': 1,
#         "factor_list": [  # 选股因子列表
#             # ** 因子格式说明 **
#             # 因子名称（与 '因子库' 文件中的名称一致），排序方式（True 为升序，False 为降序），因子参数，因子权重
#             ('成交额缩量因子', True, (10, 60), 1),
#             # ('成交额缩波因子', True, (10, 60), 1),
#             # ('Ret', True, 5, 1),
#             # ('Ret', True, 10, 1),
#             # ('收盘价', True, None, 1),
#             # ('换手率', True, 5, 1),
#             # ('换手率', True, 1, 1),
#             # 案例说明：使用'市值.py'因子，从小到大排序（越小越是我想要），None表示无额外参数，后面计算复合选股因子的时候权重为1
#             # 可添加多个选股因子
#         ],
#         "filter_list": [
#             # ('月份', [2], 'val:==1'),  # 只在2月份选股
#             # ('市值', None, 'pct:<=0.8'),
#             # ('月份', [4,], 'val:!=1'),  # 不在4月份选股
#             # ('Ret', 5, 'pct:<=0.1'),
#             # ('收盘价', None, 'pct:<=0.5'),
#             # ('换手率', 1, 'pct:<=0.8'),
#             # ('成交额缩量因子', (10, 60), 'pct:<=0.6',True),
#
#         ]  # 过滤因子列表
#     },
#     {
#         'name': '策略2',  # 策略名
#         'hold_period': 'W',  # 持仓周期，W 代表周，M 代表月，还支持日频：3D、5D、10D
#         'offset_list': [0, 1, 2],
#         'select_num': 10,  # 选股数量，可以是整数，也可以是小数，比如 0.1 表示选取 10% 的股票
#         'cap_weight': 1,
#         "factor_list": [  # 选股因子列表
#             # ** 因子格式说明 **
#             # 因子名称（与 '因子库' 文件中的名称一致），排序方式（True 为升序，False 为降序），因子参数，因子权重
#             # ('成交额缩量因子', True, (10, 60), 1),
#             # ('成交额缩波因子', True, (10, 60), 1),
#             # ('Ret', True, 5, 1),
#             # ('Ret', True, 10, 1),
#             # ('收盘价', True, None, 1),
#             ('市值', True, None, 1),
#             # ('换手率', True, 5, 1),
#             # ('换手率', True, 1, 1),
#             # 案例说明：使用'市值.py'因子，从小到大排序（越小越是我想要），None表示无额外参数，后面计算复合选股因子的时候权重为1
#             # 可添加多个选股因子
#         ],
#         "filter_list": [
#             # ('月份', [2], 'val:==1'),  # 只在2月份选股
#             # ('市值', None, 'pct:<=0.8'),
#             # ('月份', [4,], 'val:!=1'),  # 不在4月份选股
#             # ('Ret', 5, 'pct:<=0.1'),
#             # ('收盘价', None, 'pct:<=0.5'),
#             ('换手率', 1, 'pct:<=0.8'),
#             # ('成交额缩量因子', (10, 60), 'pct:<=0.6',True),
#
#         ],  # 过滤因子列表
#         # ** 换仓时间 **
#         # 选股日换仓的时候，我们可以自定义换仓的时间点
#         # - 'close-open'：选股日收盘前卖出，交易日开盘后买入（隔日换仓）；
#         # - 'open'：交易日开盘后先卖出，交易日开盘后再买入（日内早盘）；
#         # - 'close'：选股日收盘前卖出，选股日收盘前再买入（日内尾盘）；
#         # 默认是 'close-open'，表示收盘买，下个开盘买，即隔日换仓
#         'rebalance_time': 'close-open',
#     }
# ]

# backtest_name = '方天画戟'  # 回测的策略组合的名称。可以自己任意取。一般建议，一个回测组，就是实盘中的一个账户。
# # 策略明细
#
# param = {'reb_time': '0945-close', 'factor1_param': 122, 'factor1_scending': False,
#          'factor2_param': 248, 'factor2_ascending': True}
#
# strategy_list = [
#     {
#         'name': '小市值_周黎明',
#         'hold_period': 'W',
#         'offset_list': list(range(5)),
#         'select_num': 5,
#         'cap_weight': 1,
#         'rebalance_time': param['reb_time'],
#         'factor_list': [
#             ('Ret',param['factor1_scending'], param['factor1_param'],  100),
#             ('Ret', param['factor1_scending'], param['factor1_param'], 0.2),
#             ('二级行业', param['factor2_ascending'], param['factor2_param'], 2),
#             ('市值', True, '', 1),
#         ],
#         'filter_list': [
#             ('成交额Mean', 80, 'val:>=5000_0000', True),
#             ('月份', [1], 'val:!=1'),
#             ('月份', [4], 'val:!=1'),
#             # ('国九条因子25', '国九条Power', 'val:!=1')
#         ]
#     },
#     # {'name': '低波大市值', 'hold_period': 'W', 'offset_list': [0, 1, 2, 3, 4], 'select_num': 5, 'cap_weight': 1,
#     #  'rebalance_time': 'open', 'factor_list': [('市值', False, '', 1), ('波动率', True, 250, 1)],
#     #  'filter_list': [('收盘价', '', 'val:<15', True)]}
# ]

# backtest_name = '纯小市值'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': '5',
#         'offset_list': range(5),
#         'select_num': 30,
#         'cap_weight': 1,
#         'rebalance_time': '0955-0955',
#         'factor_list': [
#             ('市值', True, '', 1),
#             ('开盘至今涨幅', True, '0955', ('前25%择时', 0.4)),
#             # ('开盘至今涨幅', False, '0955', ('前25%择时', 'BULLISH_ALIGN', 0.8, 0.5)),  # BULLISH_ALIGN, KDJ
#         ],
#         'filter_list': [
#             ('成交额Mean', 5, 'val:>=5000_0000', True),
#             ('月份', [1], 'val:!=1', True),
#             ('月份', [4], 'val:!=1', True),
#             # ('月份', [9], 'val:!=1', True),
#             # ('小市值月份', '', 'val:!=1')
#             ]
#     }
#     # for t in ['0935', '0945', '0955']
#     # for month in range(1, 13)
# ]

# backtest_name = 'ST'
# strategy_list = [
#     {
#         'name': '小市值_周黎明_定风波择时',
#         'hold_period': 'W',
#         'offset_list': range(5),
#         'select_num': 5,
#         'cap_weight': 1,
#         'rebalance_time': '0935-0935',
#         'factor_list': [
#             ('Ret', False, 5, 100),
#             ('Ret', False, 20, 0.2),
#             ('一级行业', False, '', 2),
#             ('市值', True, '', 1),
#             ('开盘至今涨幅', True, '0935', ('前25%择时', 0.4)),
#             # ('开盘至今涨幅', True, '0935', ('全市场择时', 0.4)),
#             # ('开盘至今涨幅', True, '0945', ('全市场择时', 0.4)),
#             ('前收盘至开盘涨幅', True, '', ('全市场择时', 0.4)),
#             ],
#         'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
#     }
# ]

# backtest_name = '瞎练0520_定风波'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': '5',
#         'offset_list': range(5),
#         'select_num': 5,
#         'cap_weight': 1,
#         'rebalance_time': '0955-0955',
#         'factor_list': [
#             # ('Ret', True, 21, 5),
#             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
#             ('市值', True, '', 1),
#             ('开盘至今涨幅', False, '0955', ('前1000择时', 0.7)),
#             ('开盘修复速率', False, '', 1),
#             # ('红利因子', True, '', 1)
#         ],
#         'filter_list': [
#             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
#             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
#             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
#             # ('zhonghu', 13, 'val:<>0.6|0.94'),
#             ('月份', [1, 4], 'val:!=1')
#         ]
#     },
# ]

# backtest_name = '中等生策略'
# strategy_list = [
#         {
#             'name': '中等生策略',
#             'hold_period': 'W',
#             'offset_list': [0],
#             'select_num': 5,
#             'cap_weight': 1,
#             'rebalance_time': 'open',
#             'factor_list': [
#                 ('指数相关性', False, ['sh000300', 20], 1), # sh00001 上证 sh000016 上证50 sh000300 沪深300 sz399006 创业板指
#                 ('指数相关性', False, ['sh932000', 20], 1), # sh000905 中证500 sh000852 中证1000 sh932000 中证2000
#                 ('Ret', False, 10, 500),
#                 ('Ret', False, 60, 0.5),
#                 # ('一级行业', False, '', 2),
#                 ('二级行业', False, '', 9),
#                 ('市值', True, '', 1),
#             ],
#             'filter_list': [
#                 ('月份', [12], 'val:!=1'),
#                 ('月份', [4], 'val:!=1'),
#                 # ('月份', [1], 'val:!=1'),
#             ]
#         }
#         # for inde in ['sh000300', 'sh000905', 'sh932000', 'sz399006']
#         # for n in [3, 5, 10, 20, 30, 60]
#         # for month in range(1, 13)
#     ]

# backtest_name = "策略组A"  # 回测的策略组合的名称。可以自己任意取。一般建议，一个回测组，就是实盘中的一个账户。
# # 策略明细
# strategy_list = [
#     {
#         'name': '早盘轮动策略_分红_基本面优化_动态阈值',
#         'hold_period': '5',
#         'offset_list': range(5),
#         'select_num': 10,
#         'cap_weight': 1,
#         'rebalance_time': '0945-0945',
#         'factor_list': [
#             # --------分红策略参数--------------------------------------
#             ('红利因子', False, '分红率_登记日', 0.2),
#             ('红利因子', True, '连续分红年份', 1),
#             ('波动率', True, 250, 1),
#             ('归母净利润同比', True, '', 0),
#             ('一级行业', True, '', 1),
#
#             # --小市值基本面优化参数------------------------------------
#             ('市值', True, None, 1),
#             ('归母净利润同比增速', False, 60, 1),
#             ('ROE', False, '单季', 0.8),
#             ('成交额Mean', None, 5, 1000_0000),
#
#             ('开盘至今涨幅', False, '0945', ('前25%择时', 'BULLISH_ALIGN', 0.8, 0.5)),#BULLISH_ALIGN, KDJ,MACD,MACD2
#         ],
#         'filter_list': []
#     },
# ]
# 上市至今交易天数
#
# backtest_name = '瞎练0520'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': 'W',
#         'offset_list': range(5),
#         'select_num': 30,
#         'cap_weight': 1,
#         'rebalance_time': 'open',
#         'factor_list': [
#             # ('alpha95', True, 5, 1),
#             # ('Ret', True, 90, 1),
#             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
#             ('市值', True, '', 1),
#             # ('归母净利润同比增速', False, 2, 0.5),
#             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
#             # ('红利因子', True, '', 1)
#         ],
#         'filter_list': [
#             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
#             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
#             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
#             # ('归母净利润同比增速', 60, 'pct:<=0.6', False),
#             # ('成交额缩波因子', (90, 13), 'pct:>=0.7', False),
#             # ('Ret', 90, 'pct:<=0.4', True),
#             ('月份', [1], 'val:!=1'),
#             ('月份', [4], 'val:!=1'),
#             ('月份', [12], 'val:!=1')
#         ]
#     },
# ]


# backtest_name = '成交额STD_筹码胜率'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': 'W',
#         'offset_list': range(5),
#         'select_num': 30,
#         'cap_weight': 1,
#         'rebalance_time': 'open',
#         'factor_list': [
#             # ('Ret', True, 21, 5),
#             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
#             ('市值', True, '', 1),
#             # ('一级行业', False, '', 1),
#             # ('归母净利润同比增速', False, 60, 0.5),
#             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
#             ('alpha95', True, 20, 5)
#         ],
#         'filter_list': [
#             ('筹码相关因子', '胜率', 'pct:<=0.2', False),
#             # ('alpha95', 20, 'pct:<0.5', False),
#             # ('SCR', [20, 90], 'pct:<0.5', False),
#             # ('Ret', 60, 'pct:<0.5', False),
#             # ('归母净利润同比增速', 60, 'pct:<=0.1', False),
#             # ('成交额缩波因子', (13, 90), 'pct:>=0.7', False),
#             # ('异常涨跌幅', (90, 2), 'pct:<=0.2', False),
#             # ('成交额Mean', 5, 'val:>=5000', True),
#             ('月份', [1], 'val:!=1'),
#             ('月份', [4], 'val:!=1'),
#             ('月份', [12], 'val:!=1')
#         ]
#     }
#     # for x in range(1, 13)
# ]
#
# backtest_name = '行业大票'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': 'W',
#         'offset_list': range(5),
#         'select_num': 30,
#         'cap_weight': 1,
#         'rebalance_time': 'open',
#         'factor_list': [
#             # ('Ret', True, 21, 5),
#             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
#             ('市值', True, '', 1),
#             ('一级行业', False, '', 1),
#             # ('归母净利润同比增速', False, 60, 0.5),
#             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
#             # ('红利因子', True, '', 1)
#         ],
#         'filter_list': [
#             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
#             # ('alpha95', 20, 'pct:<0.1', False),
#             ('SCR', [20, 90], 'pct:<0.5', False),
#             # ('Ret', 60, 'pct:<0.5', False),
#             # ('归母净利润同比增速', 60, 'pct:<=0.6', False),
#             # ('成交额缩波因子', (13, 90), 'pct:>=0.7', False),
#             # ('异常涨跌幅', (90, 2), 'pct:>=0.7', False),
#             # ('成交额STD', 5, 'pct:<=0.7', True),
#             # ('月份', [1], 'val:!=1'),
#             # ('月份', [4], 'val:!=1'),
#             # ('月份', [1, 4], 'val:!=1')
#         ]
#     },
# ]
#
#
# backtest_name = '筹码基本面'
# strategy_list = [
#     {
#         'name': backtest_name,
#         'hold_period': 'W',
#         'offset_list': range(5),
#         'select_num': 5,
#         'cap_weight': 1,
#         'rebalance_time': '0945-0945',
#         'factor_list': [
#             # ('Ret', True, 21, 5),
#             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
#             ('市值', True, '', 1),
#             # ('归母净利润同比增速', False, 2, 0.5),
#             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
#             # ('红利因子', True, '', 1)
#         ],
#         'filter_list': [
#             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
#             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
#             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
#             ('归母净利润同比增速', 60, 'pct:<=0.6', False),
#             # ('光头阴线', '', 'val:>1')
#             # ('成交额缩波因子', (90, 13), 'pct:>=0.7', False),
#             # ('成交额STD', 5, 'pct:<=0.7', True),
#             # ('月份', [month], 'val:!=1'),
#             # ('月份', [4], 'val:!=1'),
#             # ('月份', [1, 4], 'val:!=1')
#         ],
#         # 'timing': {
#         #     'name': '定风波1P5择时',  # 择时策略名称
#         #     'limit': 100,
#         #     'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
#         #                     ('隔夜涨跌幅', False, None, 1, '开盘价'),
#         #                     # ('开盘至今涨幅', False, None, 1, '0945'),
#         #                     # ('隔夜涨跌幅', False, None, 1, '0935'),
#         #                     # ('次日涨跌停状态', False, '涨停', 1, '0945'),
#         #                     # ('次日涨跌停状态', False, '跌停', 1, '0945')
#         #                     ],
#         #     'params': 0.8
#         # }
#     } #for month in range(1, 13) for y in [60, 90, 120, 150, 180, 210, 240]
#     ,
# # {
# #         'name': backtest_name,
# #         'hold_period': '5',
# #         'offset_list': range(5),
# #         'select_num': 3,
# #         'cap_weight': 1,
# #         'rebalance_time': '0945-0945',
# #         'factor_list': [
# #             # ('Ret', True, 21, 5),
# #             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
# #             ('市值', True, '', 1),
# #             # ('归母净利润同比增速', False, 2, 0.5),
# #             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
# #             # ('红利因子', True, '', 1)
# #         ],
# #         'filter_list': [
# #             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
# #             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
# #             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
# #             ('归母净利润同比增速', 60, 'pct:<=0.6', False),
# #             # ('成交额缩波因子', (90, 13), 'pct:>=0.7', False),
# #             # ('成交额STD', 5, 'pct:<=0.7', True),
# #             # ('月份', [month], 'val:!=1'),
# #             # ('月份', [4], 'val:!=1'),
# #             # ('月份', [1, 4], 'val:!=1')
# #         ],
# #         'timing': {
# #             'name': '定风波1P5择时',  # 择时策略名称
# #             'limit': 100,
# #             'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
# #                             ('隔夜涨跌幅', False, None, 1, '开盘价'),
# #                             # ('开盘至今涨幅', False, None, 1, '0945'),
# #                             # ('隔夜涨跌幅', False, None, 1, '0935'),
# #                             # ('次日涨跌停状态', False, '涨停', 1, '0945'),
# #                             # ('次日涨跌停状态', False, '跌停', 1, '0945')
# #                             ],
# #             'params': 0.8
# #         }
# #     },
# # {
# #         'name': backtest_name,
# #         'hold_period': '3',
# #         'offset_list': range(3),
# #         'select_num': 3,
# #         'cap_weight': 1,
# #         'rebalance_time': '0945-0945',
# #         'factor_list': [
# #             # ('Ret', True, 21, 5),
# #             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
# #             ('市值', True, '', 1),
# #             # ('归母净利润同比增速', False, 2, 0.5),
# #             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
# #             # ('红利因子', True, '', 1)
# #         ],
# #         'filter_list': [
# #             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
# #             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
# #             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
# #             ('归母净利润同比增速', 60, 'pct:<=0.6', False),
# #             # ('成交额缩波因子', (90, 13), 'pct:>=0.7', False),
# #             # ('成交额STD', 5, 'pct:<=0.7', True),
# #             # ('月份', [month], 'val:!=1'),
# #             # ('月份', [4], 'val:!=1'),
# #             # ('月份', [1, 4], 'val:!=1')
# #         ],
# #         'timing': {
# #             'name': '定风波1P5择时',  # 择时策略名称
# #             'limit': 100,
# #             'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
# #                             ('隔夜涨跌幅', False, None, 1, '开盘价'),
# #                             # ('开盘至今涨幅', False, None, 1, '0945'),
# #                             # ('隔夜涨跌幅', False, None, 1, '0935'),
# #                             # ('次日涨跌停状态', False, '涨停', 1, '0945'),
# #                             # ('次日涨跌停状态', False, '跌停', 1, '0945')
# #                             ],
# #             'params': 0.8
# #         }
# #     },
# # {
# #         'name': backtest_name,
# #         'hold_period': '2W',
# #         'offset_list': range(2),
# #         'select_num': 3,
# #         'cap_weight': 1,
# #         'rebalance_time': '0945-0945',
# #         'factor_list': [
# #             # ('Ret', True, 21, 5),
# #             # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
# #             ('市值', True, '', 1),
# #             # ('归母净利润同比增速', False, 2, 0.5),
# #             # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
# #             # ('红利因子', True, '', 1)
# #         ],
# #         'filter_list': [
# #             # ('筹码相关因子', '胜率', 'val:>2.2e-5'),
# #             ('筹码相关因子', '筹码集中度', 'val:>0.26'),
# #             # ('SCR', [20, 40], 'val:<>-0.071|-0.047'),
# #             ('归母净利润同比增速', 60, 'pct:<=0.6', False),
# #             # ('成交额缩波因子', (90, 13), 'pct:>=0.7', False),
# #             # ('成交额STD', 5, 'pct:<=0.7', True),
# #             # ('月份', [month], 'val:!=1'),
# #             # ('月份', [4], 'val:!=1'),
# #             # ('月份', [1, 4], 'val:!=1')
# #         ],
# #         'timing': {
# #             'name': '定风波1P5择时',  # 择时策略名称
# #             'limit': 100,
# #             'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
# #                             ('隔夜涨跌幅', False, None, 1, '开盘价'),
# #                             # ('开盘至今涨幅', False, None, 1, '0945'),
# #                             # ('隔夜涨跌幅', False, None, 1, '0935'),
# #                             # ('次日涨跌停状态', False, '涨停', 1, '0945'),
# #                             # ('次日涨跌停状态', False, '跌停', 1, '0945')
# #                             ],
# #             'params': 0.8
# #         }
# #     }
# ]

del_cache = True
n_jobs = os.cpu_count() - 3

from 策略库.小市值_周黎明2 import backtest_name, strategy_list
from 实盘.北交所策略 import backtest_name, strategy_list

# for i in strategy_list:
#     i['select_num'] = 5

# excluded_boards = []  # 排除板块，比如 cyb 表示创业板，kcb 表示科创板 ('kcb', '科创板'), ('cyb', '创业板'), ('bj', '北交所')
excluded_boards = []  # 同时过滤创业板和科创板

# 上市至今交易天数
days_listed = 250
# 整体资金使用率，也就是用于模拟的资金比例
total_cap_usage = 100 / 100  # 100%表示用全部的资金买入，如果是0.5就是使用一半的资金来模拟交易

# ====================================================================================================
# 4️⃣ 模拟交易配置
# 以下参数几乎不需要改动
# ====================================================================================================
initial_cash = 100_0000  # 初始资金10w
# initial_cash = 1_0000_0000  # 初始资金10w
# 手续费
c_rate = 10 / 10000
# 印花税
t_rate = 1 / 1000

# ====================================================================================================
# 5️⃣ 其他配置
# 以下参数几乎不需要改动
# ====================================================================================================


# ==== factor_col_limit 介绍 ====
factor_col_limit = 200  # 内存优化选项，一次性计算多少列因子。8 是16G电脑的推荐配置
# - 数字越大，计算速度越快，但同时内存占用也会增加。
# - 该数字是在 "因子数量 * 参数数量" 的基础上进行优化的。
#   - 例如，当你遍历 200 个因子，每个因子有 10 个参数，总共生成 2000 列因子。
#   - 如果 `factor_col_limit` 设置为 64，则计算会拆分为 ceil(2000 / 64) = 32 个批次，每次最多处理 64 列因子。
# - 以上数据仅供参考，具体值会根据机器配置、策略复杂性、回测周期等有所不同。建议大家根据实际情况，逐步测试自己机器的性能极限，找到适合的最优值。


# =====参数预检查=====
runtime_folder = get_folder_path(runtime_data_path, '运行缓存2')
if Path(data_center_path).exists() is False:
    print(f'数据中心路径不存在：{data_center_path}，请检查配置或联系助教，程序退出')
    exit()

# 强制转换为 Path 对象
data_center_path = Path(data_center_path)
runtime_data_path = Path(runtime_data_path)
