"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
# import re
#
# import pandas as pd

backtest_name = '北交所策略'
strategy_list = [
    {'name': backtest_name ,
     'hold_period': '2D',
     'offset_list': [0, 1],
     'select_num': 5,
     'cap_weight': 1,
     'rebalance_time': 'close-open',
     'factor_list': [
         ('市值', True, None, 2),
         # ('alpha95', True, 5, 1),
         ('Ret', True, 13, 1),
        ("最大回撤", True, 60, 1),
            ('波动率', False, 13, 1)
     ],
     'filter_list': [
         # ('成交额相关因子', ['均值', 5], 'val:>=5e7'),
         # ('昨日涨停', '涨停', 'val:!=1', True),
         ('交易所', 'bj', 'val:==1'),
         ('alpha95', 5, 'pct:<=0.8', True),
         # ('筹码相关因子', '筹码集中度', 'val:>=0.26'),
         # ('月份', [12], 'val:!=1'),
     ],
     }
    # for n in [3, 5, 8, 13, 21, 30]
]


'''
累积净值                            4.36
年化收益                         199.60%
最大回撤                         -31.60%
最大回撤开始时间     2024-12-03 00:00:00
最大回撤结束时间     2025-01-02 00:00:00
年化收益/回撤比                     6.32

     'factor_list': [
         ('市值', True, None, 3),
         # ('alpha95', True, 5, 1),
         ('Ret', True, 5, 1),
     ],
     'filter_list': [
         # ('成交额相关因子', ['均值', 5], 'val:>=5e7'),

         ('交易所', 'bj', 'val:==1'),
         ('alpha95', 5, 'pct:<=0.8', True),
         # ('筹码相关因子', '筹码集中度', 'val:>=0.26'),
         # ('月份', [12], 'val:!=1'),
     ],
     
     
累积净值                            7.06
年化收益                         328.77%
最大回撤                         -30.53%
最大回撤开始时间     2024-12-03 00:00:00
最大回撤结束时间     2025-01-13 00:00:00
年化收益/回撤比                    10.77
strategy_list = [
    {
        "name": "小市值_量价优化",
        "hold_period": "W",
        "offset_list": range(0, 5),
        "select_num": 5,
        "cap_weight": 1,
        "rebalance_time": "open",
        "factor_list": [
            ("市值", True, None, 2),
            ("Ret", True, 13, 1),
            ("最大回撤", True, 60, 1)
        ],
        "filter_list": [
            ("交易所", "bj", "val:==1"),
            ("alpha95", 5, "pct:<=0.8", True)
        ]
    }
]



累积净值                            9.79
年化收益                         447.11%
最大回撤                         -26.64%
最大回撤开始时间     2024-12-03 00:00:00
最大回撤结束时间     2024-12-30 00:00:00
年化收益/回撤比                    16.79
strategy_list = [
    {
        "name": "小市值_量价优化",
        "hold_period": "3",
        "offset_list": range(3),
        "select_num": 5,
        "cap_weight": 1,
        "rebalance_time": "open",
        "factor_list": [
            ("市值", True, None, 2),
            ("Ret", True, 13, 1),
            ("最大回撤", True, 60, 1)
        ],
        "filter_list": [
            ("交易所", "bj", "val:==1"),
            ("alpha95", 5, "pct:<=0.8", True)
        ]
    }
]


累积净值                            9.65
年化收益                         441.32%
最大回撤                         -27.57%
最大回撤开始时间     2024-12-03 00:00:00
最大回撤结束时间     2024-12-30 00:00:00
年化收益/回撤比                     16.0
strategy_list = [
    {
        "name": "小市值_量价优化",
        "hold_period": "2",
        "offset_list": range(2),
        "select_num": 5,
        "cap_weight": 1,
        "rebalance_time": "open",
        "factor_list": [
            ("市值", True, None, 2),
            ("Ret", True, 13, 1),
            ("最大回撤", True, 60, 1)
        ],
        "filter_list": [
            ("交易所", "bj", "val:==1"),
            ("alpha95", 5, "pct:<=0.8", True)
        ]
    }
]
'''

