
bys_result_filter_list = [
    "((`上方筹码集中度` < 0.017) | (`上方筹码集中度` > 0.24))",
    "(`筹码偏离度` < 0.04)",
    "(`成交额_std10_3` > 7e6) & (`成交额_std10_3` < 27e6)",
    # "(`均线粘合系数` > 0.05)",
]

# "((`均线粘合系数` < 0.0031) | (`均线粘合系数` > 0.033))",
# bys_result_sort_dict = {
#     "总市值": {
#         "排序方式": "正序",
#         "排序权重": 1.0
#     }
# }

backtest_name = '瞎练'
strategy_list = [
    {
        'name': backtest_name,
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 30,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [
            # ('Ret', True, 21, 5),
            # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
            ('市值', True, '', 1),
            # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
            # ('红利因子', True, '', 1)
        ],
        'filter_list': [
            ('筹码相关因子', '上方筹码集中度', 'val:>=0.24'),
            ('筹码相关因子', '筹码偏离度', 'val:<=0.04'),
            # ('成交额相关因子', ['stdn_m', 10, 3], 'val:>7e6'),
            ('成交额相关因子', ['stdn_m', 10, 3], 'val:<27e6'),
            ('月份', [1, 4], 'val:!=1')
        ],
        'timing': {
            'name': '定风波1P5择时',  # 择时策略名称
            'limit': 100,
            'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
                            ('隔夜涨跌幅', False, None, 1, '开盘价'),
                            # ('开盘至今涨幅', False, None, 1, '0945'),
                            # ('隔夜涨跌幅', False, None, 1, '0935'),
                            # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                            # ('次日涨跌停状态', False, '跌停', 1, '0945')
                            ],
            'params': 0.85
        }
    },
    {
        'name': backtest_name,
        'hold_period': '3D',
        'offset_list': [0,2],
        'select_num': 10,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [
            # ('Ret', True, 21, 5),
            # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
            ('市值', True, '', 1),
            # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
        ],
        'filter_list': [
            # ('筹码相关因子', '上方筹码集中度', 'val:<>0.017|0.24'),
            ('筹码相关因子', '上方筹码集中度', 'val:>=0.24'),
            ('筹码相关因子', '筹码偏离度', 'val:<=0.04'),
            # ('成交额相关因子', ['stdn_m', 10, 3], 'val:>7e6'),
            ('成交额相关因子', ['stdn_m', 10, 3], 'val:<27e6'),
            # ('月份', [1, 4], 'val:!=1')
        ],
        'timing': {
            'name': '定风波1P5择时',  # 择时策略名称
            'limit': 100,
            'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
                            ('隔夜涨跌幅', False, None, 1, '开盘价'),
                            # ('开盘至今涨幅', False, None, 1, '0945'),
                            # ('隔夜涨跌幅', False, None, 1, '0935'),
                            # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                            # ('次日涨跌停状态', False, '跌停', 1, '0945')
                            ],
            'params': 0.85
        }
    },
{
        'name': backtest_name,
        'hold_period': '2D',
        'offset_list': [1],
        'select_num': 30,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [
            # ('Ret', True, 21, 5),
            # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
            ('市值', True, '', 1),
            # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
        ],
        'filter_list': [
            # ('筹码相关因子', '上方筹码集中度', 'val:<>0.017|0.24'),
            ('筹码相关因子', '上方筹码集中度', 'val:>=0.24'),
            ('筹码相关因子', '筹码偏离度', 'val:<=0.04'),
            # ('成交额相关因子', ['stdn_m', 10, 3], 'val:>7e6'),
            ('成交额相关因子', ['stdn_m', 10, 3], 'val:<27e6'),
            # ('月份', [1, 4], 'val:!=1')
        ],
        'timing': {
            'name': '定风波1P5择时',  # 择时策略名称
            'limit': 100,
            'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
                            ('隔夜涨跌幅', False, None, 1, '开盘价'),
                            # ('开盘至今涨幅', False, None, 1, '0945'),
                            # ('隔夜涨跌幅', False, None, 1, '0935'),
                            # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                            # ('次日涨跌停状态', False, '跌停', 1, '0945')
                            ],
            'params': 0.85
        }
    },
{
        'name': backtest_name,
        'hold_period': '5D',
        'offset_list': [0, 1, 3 ],
        'select_num': 10,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [
            # ('Ret', True, 21, 5),
            # ('成交额相关因子', False, ['stdn_m', 10, 3], 1),
            ('市值', True, '', 1),
            # ('开盘至今涨幅', False, '0945', ('全市场择时', 0.4)),
        ],
        'filter_list': [
            # ('筹码相关因子', '上方筹码集中度', 'val:<>0.017|0.24'),
            ('筹码相关因子', '上方筹码集中度', 'val:>=0.24'),
            ('筹码相关因子', '筹码偏离度', 'val:<=0.04'),
            # ('成交额相关因子', ['stdn_m', 10, 3], 'val:>7e6'),
            ('成交额相关因子', ['stdn_m', 10, 3], 'val:<27e6'),
            # ('月份', [1, 4], 'val:!=1')
        ],
        'timing': {
            'name': '定风波1P5择时',  # 择时策略名称
            'limit': 100,
            'factor_list': [('开盘至今涨幅', False, None, 1, '0945'),
                            ('隔夜涨跌幅', False, None, 1, '开盘价'),
                            # ('开盘至今涨幅', False, None, 1, '0945'),
                            # ('隔夜涨跌幅', False, None, 1, '0935'),
                            # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                            # ('次日涨跌停状态', False, '跌停', 1, '0945')
                            ],
            'params': 0.85
        }
    }
]

