"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行

震荡修复动量策略 - 基于大幅震荡后股价修复速度的动量策略
"""

# ================================ 震荡修复动量策略配置 ================================

# 1. 大跌修复策略 - 捕捉超跌反弹机会
big_drop_recovery_strategy = {
    'name': '大跌修复动量策略',
    'hold_period': 'W',
    'offset_list': [0, 1, 2, 3, 4],
    'select_num': 20,
    'cap_weight': 1,
    'rebalance_time': '0945-0945',
    'factor_list': [
        ('跌停起步改', False, (4, 20, 1.05, 'big_drop', 0.09), 0.6),    # 大跌综合修复动量，权重60%
        ('市值', True, None, 0.4),                                        # 市值因子，权重40%
    ],
    'filter_list': [
        ('ST因子', None, 'val:==0'),                                      # 过滤ST股票
        ('上市至今交易天数', None, 'val:>=250'),                          # 过滤上市不足1年股票
    ],
    'timing': {
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.85
    }
}

# 2. 高波动修复策略 - 捕捉震荡后的趋势确认
volatility_recovery_strategy = {
    'name': '高波动修复动量策略',
    'hold_period': 'W',
    'offset_list': [0, 1, 2, 3, 4],
    'select_num': 25,
    'cap_weight': 1,
    'rebalance_time': '0945-0945',
    'factor_list': [
        ('跌停起步改', False, (3, 15, 1.05, 'volatility', 0.15), 0.5),   # 高波动速度修复，权重50%
        ('跌停起步改', False, (2, 18, 1.05, 'volume_spike', 4.0), 0.3),  # 放量幅度修复，权重30%
        ('市值', True, None, 0.2),                                        # 市值因子，权重20%
    ],
    'filter_list': [
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=180'),
    ],
    'timing': {
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.80
    }
}

# 3. 放量修复策略 - 捕捉资金关注后的修复
volume_spike_recovery_strategy = {
    'name': '放量修复动量策略',
    'hold_period': 'W',
    'offset_list': [0, 1, 2, 3, 4],
    'select_num': 15,
    'cap_weight': 1,
    'rebalance_time': '0945-0945',
    'factor_list': [
        ('跌停起步改', False, (2, 18, 1.05, 'volume_spike', 4.0), 0.7),  # 放量幅度修复，权重70%
        ('市值', True, None, 0.3),                                        # 市值因子，权重30%
    ],
    'filter_list': [
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=120'),
        ('市值', None, 'val:>=50'),                                       # 过滤小市值股票
    ],
    'timing': {
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.75
    }
}

# 4. 高换手修复策略 - 捕捉活跃交易后的修复
high_turnover_recovery_strategy = {
    'name': '高换手修复动量策略',
    'hold_period': 'W',
    'offset_list': [0, 1, 2, 3, 4],
    'select_num': 18,
    'cap_weight': 1,
    'rebalance_time': '0945-0945',
    'factor_list': [
        ('跌停起步改', False, (1, 10, 1.02, 'high_turnover', 0.20), 0.6), # 高换手天数修复，权重60%
        ('跌停起步改', False, (3, 15, 1.05, 'volatility', 0.12), 0.4),    # 波动速度修复，权重40%
    ],
    'filter_list': [
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=200'),
    ],
    'timing': {
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.82
    }
}

# 5. 大涨回调修复策略 - 捕捉强势股回调的二次启动
big_rise_recovery_strategy = StrategyConfig(
    name='大涨回调修复策略',
    hold_period='W',
    offset_list=[0, 1, 2, 3, 4],
    select_num=12,
    cap_weight=1,
    rebalance_time='0945-0945',
    factor_list=[
        ('跌停起步改', False, (3, 12, 1.03, 'big_rise', 0.10), 0.8),     # 大涨速度修复，权重80%
        ('市值', True, None, 0.2),                                        # 市值因子，权重20%
    ],
    filter_list=[
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=300'),
        ('市值', None, 'val:>=100'),                                      # 过滤小市值股票
    ],
    timing={
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.88
    }
)

# 6. 传统跌停修复策略 - 经典跌停起步策略升级版
limit_down_recovery_strategy = StrategyConfig(
    name='传统跌停修复策略',
    hold_period='W',
    offset_list=[0, 1, 2, 3, 4],
    select_num=22,
    cap_weight=1,
    rebalance_time='0945-0945',
    factor_list=[
        ('跌停起步改', False, (4, 15, 1.05, 'limit_down', 0.0), 0.7),    # 跌停综合修复，权重70%
        ('市值', True, None, 0.3),                                        # 市值因子，权重30%
    ],
    filter_list=[
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=200'),
    ],
    timing={
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.85
    }
)

# 7. 多锚定综合策略 - 综合多种震荡修复信号
multi_anchor_comprehensive_strategy = StrategyConfig(
    name='多锚定震荡修复综合策略',
    hold_period='W',
    offset_list=[0, 1, 2, 3, 4],
    select_num=30,
    cap_weight=1,
    rebalance_time='0945-0945',
    factor_list=[
        ('跌停起步改', False, (4, 20, 1.05, 'big_drop', 0.09), 0.35),    # 大跌综合修复，权重35%
        ('跌停起步改', False, (3, 15, 1.05, 'volatility', 0.15), 0.25),  # 高波动速度修复，权重25%
        ('跌停起步改', False, (2, 18, 1.05, 'volume_spike', 4.0), 0.25), # 放量幅度修复，权重25%
        ('市值', True, None, 0.15),                                       # 市值因子，权重15%
    ],
    filter_list=[
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=250'),
    ],
    timing={
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.80
    }
)

# 8. 激进震荡修复策略 - 高风险高收益
aggressive_recovery_strategy = StrategyConfig(
    name='激进震荡修复策略',
    hold_period='W',
    offset_list=[0, 1, 2, 3, 4],
    select_num=10,
    cap_weight=1,
    rebalance_time='0945-0945',
    factor_list=[
        ('跌停起步改', False, (3, 8, 1.02, 'high_turnover', 0.25), 0.4),  # 高换手快速修复，权重40%
        ('跌停起步改', False, (1, 6, 1.02, 'volatility', 0.18), 0.35),    # 高波动快速修复，权重35%
        ('跌停起步改', False, (2, 10, 1.03, 'volume_spike', 5.0), 0.25),  # 大放量修复，权重25%
    ],
    filter_list=[
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=150'),
    ],
    timing={
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.70
    }
)

# 9. 稳健震荡修复策略 - 低风险稳健收益
conservative_recovery_strategy = StrategyConfig(
    name='稳健震荡修复策略',
    hold_period='W',
    offset_list=[0, 1, 2, 3, 4],
    select_num=35,
    cap_weight=1,
    rebalance_time='0945-0945',
    factor_list=[
        ('跌停起步改', False, (2, 25, 1.08, 'big_drop', 0.08), 0.4),     # 大跌稳健修复，权重40%
        ('跌停起步改', False, (4, 22, 1.06, 'limit_down', 0.0), 0.3),    # 跌停综合修复，权重30%
        ('市值', True, None, 0.3),                                        # 市值因子，权重30%
    ],
    filter_list=[
        ('ST因子', None, 'val:==0'),
        ('上市至今交易天数', None, 'val:>=300'),
        ('市值', None, 'val:>=80'),                                       # 过滤小市值股票
    ],
    timing={
        'name': '定风波1P5择时',
        'limit': 100,
        'factor_list': [
            ('开盘至今涨幅', False, None, 1, '0945'),
            ('隔夜涨跌幅', False, None, 1, '开盘价')
        ],
        'params': 0.90
    }
)

# ================================ 策略列表 ================================

# 将所有策略组织成列表，供回测主程序调用
strategy_list = [
    big_drop_recovery_strategy,           # 大跌修复动量策略
    volatility_recovery_strategy,         # 高波动修复动量策略
    volume_spike_recovery_strategy,       # 放量修复动量策略
    high_turnover_recovery_strategy,      # 高换手修复动量策略
    big_rise_recovery_strategy,           # 大涨回调修复策略
    limit_down_recovery_strategy,         # 传统跌停修复策略
    multi_anchor_comprehensive_strategy,  # 多锚定综合策略
    aggressive_recovery_strategy,         # 激进震荡修复策略
    conservative_recovery_strategy,       # 稳健震荡修复策略
]

# ================================ 策略说明 ================================

strategy_descriptions = {
    '大跌修复动量策略': {
        '描述': '专门捕捉大跌后的超跌反弹机会',
        '适用场景': '熊市反弹、超跌修复、价值回归',
        '风险等级': '中等',
        '预期年化收益': '15-25%',
        '最大回撤预期': '8-15%',
    },

    '高波动修复动量策略': {
        '描述': '捕捉大幅震荡后的方向性修复',
        '适用场景': '震荡市场、突发事件后修复、技术面修复',
        '风险等级': '较高',
        '预期年化收益': '20-35%',
        '最大回撤预期': '12-20%',
    },

    '放量修复动量策略': {
        '描述': '基于成交量异常后的价格修复',
        '适用场景': '消息面驱动、机构建仓、热点轮动',
        '风险等级': '中等',
        '预期年化收益': '18-28%',
        '最大回撤预期': '10-18%',
    },

    '多锚定震荡修复综合策略': {
        '描述': '综合多种震荡修复信号的平衡策略',
        '适用场景': '全市场环境、长期持有、风险分散',
        '风险等级': '中等',
        '预期年化收益': '16-26%',
        '最大回撤预期': '9-16%',
    },

    '激进震荡修复策略': {
        '描述': '高风险高收益的快速修复策略',
        '适用场景': '短期交易、高频调仓、激进投资',
        '风险等级': '高',
        '预期年化收益': '25-45%',
        '最大回撤预期': '15-25%',
    },

    '稳健震荡修复策略': {
        '描述': '低风险稳健收益的保守策略',
        '适用场景': '稳健投资、长期配置、风险厌恶',
        '风险等级': '低',
        '预期年化收益': '12-20%',
        '最大回撤预期': '6-12%',
    },
}

# ================================ 使用示例 ================================

if __name__ == '__main__':
    # 演示如何访问策略对象
    print("🎯 震荡修复动量策略列表")
    print("=" * 60)

    for i, strategy in enumerate(strategy_list):
        print(f"{i+1}. {strategy.name}")
        print(f"   选股数量: {strategy.select_num}")
        print(f"   因子数量: {len(strategy.factor_list)}")
        print(f"   过滤条件: {len(strategy.filter_list)}个")

        if strategy.name in strategy_descriptions:
            desc = strategy_descriptions[strategy.name]
            print(f"   风险等级: {desc['风险等级']}")
            print(f"   适用场景: {desc['适用场景']}")
        print()

    # 演示访问方式
    print("📋 访问示例:")
    print(f"strategy_list[0].name = '{strategy_list[0].name}'")
    print(f"strategy_list[0].select_num = {strategy_list[0].select_num}")
    print(f"strategy_list[0].factor_list = {strategy_list[0].factor_list}")
