import datetime
import logging
import os
import random
import traceback
from itertools import product

import numpy as np
import re
from urllib.parse import quote

from tqdm import tqdm
import pandas as pd
import logging

import tools.utils.pfunctions as pf
import tools.utils.tfunctions as tf
from core.model.backtest_config import load_config
from core.utils.log_kit import logger
from core.model.strategy_config import filter_common

from concurrent.futures import ProcessPoolExecutor, as_completed


def regen_cache(factors):
    """
    重新生成因子缓存配置

    输入示例:
    - "factor_市值_20" -> ('市值', True, 20, 1)  # 单参数直接使用
    - "factor_RSI_(14,21)" -> ('RSI', True, (14, 21), 1)  # 多参数用tuple
    - "factor_跌停起步改_(4,20,1.05,big_drop,0.09)" -> ('跌停起步改', True, (4, 20, 1.05, 'big_drop', 0.09), 1)
    """
    factor_list = []

    for factor_name in factors:
        parts = factor_name.split('_')

        if len(parts) > 2:
            # 有参数的情况: factor_名称_参数
            _, f, param_str = parts

            # 解析参数
            param_value = _parse_parameter_string(param_str)

        else:
            # 无参数的情况: factor_名称
            _, f = parts
            param_value = None

        # 生成因子配置字符串
        if param_value is None:
            factor_config = f"('{f}', True, None, 1)"
        else:
            factor_config = f"('{f}', True, {param_value}, 1)"

        factor_list.append(factor_config)

    # 生成策略文件内容
    pre = """
backtest_name = '测试用'
strategy_list = [
    {
        'name': backtest_name,
        'hold_period': '5',
        'offset_list': [2],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [
            """
    pre += ',\n\t\t\t'.join(factor_list)
    pre += """
        ],
        'filter_list': [
        ]
    }
]
    """
    print(pre)

    # 写入策略文件
    strategy_file_path = r'../策略库/测试用.py'
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.writelines(pre)

    print(f"✅ 策略文件已生成: {strategy_file_path}")
    return factor_list


def _parse_parameter_string(param_str):
    """
    解析参数字符串

    规则:
    - 单参数: 直接返回转换后的值
    - 多参数: 返回tuple格式的字符串

    示例:
    - "20" -> 20  (单参数，直接使用)
    - "(14,21)" -> (14, 21)  (多参数，使用tuple)
    - "(4,20,1.05,big_drop,0.09)" -> (4, 20, 1.05, 'big_drop', 0.09)
    """
    # 移除括号
    if param_str.startswith('(') and param_str.endswith(')'):
        param_str = param_str[1:-1]

    # 分割参数
    if ',' in param_str:
        # 多参数情况
        param_parts = [p.strip() for p in param_str.split(',')]
        converted_params = []

        for param in param_parts:
            converted_param = _convert_parameter_type(param)
            converted_params.append(converted_param)

        # 返回tuple格式字符串
        return f"({', '.join(map(str, converted_params))})"

    else:
        # 单参数情况 - 直接返回转换后的值
        return _convert_parameter_type(param_str.strip())


def _convert_parameter_type(param_str):
    """
    智能转换参数类型

    转换规则:
    1. 纯整数 -> int
    2. 小数 -> float
    3. 布尔值 -> bool
    4. 字符串 -> 'string' (带引号)
    """
    param_str = param_str.strip()

    # 尝试转换为int
    try:
        if '.' not in param_str:
            return int(param_str)
    except ValueError:
        pass

    # 尝试转换为float
    try:
        return float(param_str)
    except ValueError:
        pass

    # 布尔值处理
    if param_str.lower() in ('true', 'false'):
        return param_str.lower() == 'true'

    # 字符串处理 - 添加引号
    return f"'{param_str}'"

def gen_combinations():
    # 以下开始为构造的遍历参数
    # 需要测试的因子，要注意因子文件中para的写法，单参数、多参数的问题，最后结构是 factor_因子文件名_因子参数
    # factors_param_0 = ['BP', 'ST因子', '净利润单季同比', '上市至今交易天数', '上市至今交易天数',
    #                     '市值', '开盘修复速率', '归母净利润同比', '隔夜涨幅',
    #                    # '一级行业', '二级行业',
    #                    ]
    # factors_param_1 = ['alpha95', 'alpha95市值比', 'Ret', 'zhonghu', '平均市值', '归母净利润同比增速', '成交额Mean',
    #                    '成交额STD', '换手率', '最大回撤', '波动率', '近期停牌天数', '近期涨跌幅'
    #                    ]
    # factors_param_2 = ['SCR', '均线胜率', '异常涨跌幅', '成交额缩波因子', '成交额缩量因子']
    factors_param_text = {
        # 'EP': ['全年', '单季'],
        # 'ROE': ['全年', '单季'],
        # '国九条因子25': ['国九条Base', '国九条Power'],
        # '指数相关性': [],
        # '筹码相关因子': [
        #     '筹码集中度', '筹码偏离度', '胜率', '上方筹码集中度', '下方筹码集中度',
        #     '筹码穿透率', '主力筹码控盘率', '筹码获利比例', '筹码成本区间',
        #     '筹码波动率', '短期筹码变动', '中期筹码变动', '长期筹码变动'
        # ],
        # '分钟价格5m': ['0935', '0940', '0945', '0950', '0955', '1000', '1005', '1010', '1015', '1020',
        #                '1025', '1030', '1035', '1040', '1045', '1050', '1055', '1100', '1105', '1110',
        #                '1115', '1120', '1125', '1130', '1305', '1310', '1315', '1320', '1325', '1330', '1335',
        #                '1340', '1345', '1350', '1355', '1400', '1405', '1410', '1415', '1420', '1425', '1430',
        #                '1435', '1440', '1445', '1450', '1455'],
        # '分钟价格15m': ['0945', '1000', '1015', '1030', '1045', '1100', '1115', '1130', '1315', '1330',
        #                 '1345', '1400', '1415', '1430', '1445'],
        # '突破相关因子': [
        #     (cm, n, m)
        #     for cm in [
        #         '均线突破', '双均线突破', '通道突破', '布林带突破',
        #         '量价突破', '缺口突破', '阻力位突破', '支撑位突破',
        #         '趋势线突破', '成交量突破'
        #     ]
        #     for n in [5, 10, 20, 30, 60, 90, 120, 150, 180, 210, 240]
        #     for m in range(1, 4)
        # ]
        '窗口极值比': [
            (f"{minmax}", n)
            for minmax in ['Min', 'Max']
            for n in [3, 5, 8, 13, 21, 30, 60, 120, 250]
        ],
        '低位距离': [
            (m, n)
            for n in [3, 5, 8, 13, 21, 30, 60, 120, 250]
            for m in range(1, 6)
        ]
    }

    all_factors_params = []

    # all_factors_params += [ #无参数因子
    #     f'factor_{factor}' for factor in factors_param_0
    # ] #无参数因子
    #
    # all_factors_params += [ #单参数因子
    #     f'factor_{factor}_{param}'
    #     for factor in factors_param_1
    #     for param in [2, 3, 5, 8, 13, 20, 30, 60, 90]  # 参数为单 int
    # ] #单参数因子
    #
    # all_factors_params += [
    #     f'factor_{factor}_({n},{m})'
    #     for factor in factors_param_2
    #     for n in [2, 3, 5, 8, 13, 20, 30, 60, 90]
    #     for m in [2, 3, 5, 8, 13, 20, 30, 60, 90]
    # ] #双参数因子

    all_factors_params += [
        f'factor_{factor}_{param}'.replace("'", '').replace(' ', '')
        for factor, value in factors_param_text.items()
        for param in value
    ]

    # process_data的过滤/复合因子列表，也就是分域/复合排名
    other_factor_list = [
        {'小市值': ['factor_市值']}
        # ('zhonghu', 13, 'val:<0.6'),
        # ('月份', (1, 4), 'val:!=1'),
        # 'factor_成交额缩量因子_(10,60)'  #第二种复合的写法
    ]
    other_factor_list = [
        # ''
        {'小市值': ['factor_市值']},
        {'大市值': ['factor_市值']},
        # {'重资产行业': ['factor_一级行业']},
        # {'低价股': ['factor_市值']},
        # {'高ROE': ['factor_ROE_单季']},
        # '高分红': ['factor_分红率登记日'],
        # '中证1000': ['factor_中证1000成分股'],
        # '融资融券': ['factor_两融池'],
        # '机构调研': ['factor_机构调研池'],
        # '笛卡尔': ['factor_A股溢价率_AH股池'],
        # '非笛卡尔': ['factor_A股溢价率_AH股池'],
        # {'筹码获利': ['factor_筹码相关因子_胜率']},
        # '近日突破': ['factor_近日突破均线_(10,60)', 'factor_MA_60'],

        # '基本面': [''],
    ]

    hold_period = ['W', 3, 2]
    offsets = []
    offsets += [f'{p}_0' for p in hold_period] #只分析单offset，只是大概的因子情况
    # for hold in hold_period:
    #     if hold == 'W':
    #         offsets += [f'W_{n}' for n in range(5)]
    #     elif hold == 'W53':
    #         offsets += ['W53_0']
    #     elif isinstance(hold, int):
    #         offsets += [f'{hold}_{n}' for n in range(hold)]
    #     elif 'W' in hold:  # 2W, 3W情况
    #         offsets += [f'{hold}_{n}' for n in range(int(hold[:-1]))]
    #     elif 'M' in hold:  # 因子分析不支持，排除
    #         logger.warn('因子分析不支持 M 级别: ', hold)
    #         # exit(1)
    #     else:
    #         logger.warn('不支持的情况： ', hold)

    all_combinations = list(product(all_factors_params, other_factor_list, offsets))

    print(len(all_combinations), all_combinations[:10])#,random.choices(all_combinations, k=5))

    return all_combinations, all_factors_params


def split_condition(condition):
    # 使用正则表达式匹配条件表达式
    match = re.match(r'(\w+):([<>]=?)\s*(\d+\.?\d*e?[+-]?\d*)', condition)
    if match:
        var_name = match.group(1)
        operator = match.group(2)
        value = match.group(3)
        return var_name, operator, value
    else:
        raise ValueError("无法解析条件表达式")


def data_process(df, filter_list='', filter_type=''):
    """
    在这个函数里面处理数据，主要是：过滤，计算符合因子等等
    :param df:
    :return:
    """
    # logger.ok(filter_list)
    if isinstance(filter_list, list | tuple):
        # 'filter_list': [('筹码相关因子', '胜率', 'val:>2.2e-5'),]
        # logger.ok((filter_list, type(filter_list))
        col_name, param, args = filter_list[0], filter_list[1], filter_list[2]
        sort = True
        if len(filter_list) == 4:
            sort = filter_list[3]

        _how, _oper, _var = split_condition(args)
        # logger.ok((df.columns)
        match _how:
            case 'rank':
                df[_how] = df.groupby('交易日期')[f'"factor_{col_name}_{param}"'].rank(ascending=sort, method='first',
                                                                                       pct=False)
                # 动态构建条件表达式
                condition = f"df[{_how}] {_oper} {_var}"
                # 过滤数据
                df = df[eval(condition)]
            case 'pct':
                df[_how] = df.groupby('交易日期')[f'"factor_{col_name}_{param}"'].rank(ascending=sort, method='first',
                                                                                       pct=True)
                # 动态构建条件表达式
                condition = f"df[{_how}] {_oper} {_var}"
                # 过滤数据
                df = df[eval(condition)]
            case 'val':
                # 动态构建条件表达式
                condition = f"df[{f'"factor_{col_name}_{param}"'}] {_oper} {_var}"
                # 过滤数据
                # logger.ok((condition)
                df = df[eval(condition)]

        # filter_condition = filter_common(df, filter_list)
        # df = df[filter_condition]
        pass
    elif isinstance(filter_list, str): # 使用固定的分域
        pass
        match filter_list:
            case '小市值':
                # print(f'{filter_list}, we are here')
                df['总市值分位数'] = df.groupby('交易日期')['总市值'].rank(pct=True)
                df = df[df['总市值分位数'] < 0.3]
            case '大市值':
                df['总市值分位数'] = df.groupby('交易日期')['总市值'].rank(pct=True)
                df = df[df['总市值分位数'] >= 0.9]
            case '重资产行业':
                indus_name = ['银行', '煤炭', '钢铁', '有色金属']
                df = df[~df['factor_一级行业'].isin(indus_name)]
            # case '低价股':
            #     df = df[df['收盘价'] < 10]
            case '高ROE':
                df['ROE排名'] = df.groupby('交易日期')['factor_ROE_单季'].rank(ascending=False, method='min', pct=True)
                df = df[df['ROE排名'] < 0.2]
            case '高分红':
                df['分红率分位数'] = df.groupby(['交易日期'])['factor_分红率登记日'].rank(ascending=False, pct=True)
                df = df[df['分红率分位数'] <= 0.2]
                df = df[df['factor_连续分红年份'] > 1]
            case '中证1000':
                df = df[df['factor_中证1000成分股'] == 'Y']
            case '融资融券':
                df = df[df['factor_融资融券_两融池'] == 'Y']
            case '机构调研':
                df = df[df['factor_机构调研池'] == 'Y']
            case '笛卡尔':
                df = df[df['factor_A股溢价率_AH股池'] == 'Y']
            case '非笛卡尔':
                df = df[df['factor_A股溢价率_AH股池'] != 'Y']
            case '筹码获利':
                df['胜率排名'] = df.groupby('交易日期')['factor_筹码相关因子_胜率'].rank(ascending=False, method='min', pct=True)
                df = df[df['胜率排名'] < 0.2]
            # case '近日突破':
            #     df = df[(df['factor_近日突破均线_(10,60)'] > 0) & (df[df['收盘价'] > df['factor_MA_60']])]
            case '基本面':
                pass
        # 案例1：增加分域的代码
        # df['总市值分位数'] = df.groupby('交易日期')['总市值'].rank(pct=True)
        # df = df[df['总市值分位数'] >= 0.9]
        # df = df[df['收盘价'] < 100]
    elif "factor_" in filter_list:  # 有传参数，适配第二种情况
        df['复合因子'] = df[filter_list]

        # 案例2：增加计算复合因子的代码
        # df['rank'] = df.groupby('交易日期')['总市值'].rank()
        # df['成交额排名'] = df.groupby('交易日期')['成交额'].rank(ascending=False)
        # df['复合因子'] = df['总市值排名'] + df['成交额排名']

        # df['成交额市值复合因子'] = df['factor_成交额缩量因子_(10,60)'] + df['factor_市值_None']
    return df


"""
由于底层数据是1D级别的，所以数据量特别大，因子分析的计算量也比较大
为了减少内存开销，增加计算速度，因子分析默认只针对5_0周期进行分析
可以通过更改配置实现针对其他周期的计算，但不支持M系列的周期
"""

def factor_analysis(name, func, cfg, boost):
    # 因子分析需要用到的配置数据
    # name = ('factor_alpha95_2', ('zhonghu', 13, 'val:<>0.6|0.94'), '2W_0')
    # print(name, len(name), type(name))
    name = eval(name)
    cfg.fa_name, data_process_filter, cfg.period_offset = name #在校验时切换为str了，需要切换回来

    cfg.func = func
    cfg.keep_cols = ['交易日期', '股票代码', '股票名称', '下日_是否交易', '下日_开盘涨停', '下日_是否ST',
                     '下日_是否退市', '上市至今交易天数', cfg.fa_name, '新版申万一级行业名称', '下周期涨跌幅',
                     '下周期每天涨跌幅']
    cfg.ind_name_change = {'采掘': '煤炭', '化工': '基础化工', '电气设备': '电力设备', '休闲服务': '社会服务',
                           '纺织服装': '纺织服饰', '商业贸易': '商贸零售'},

    start_time = datetime.datetime.now()

    # 读取因子数据
    factors_pkl = [_dir[:-4] for _dir in os.listdir(cfg.get_result_folder().parent.parent / cfg.runtime_folder) if
                   _dir.startswith('factor_')]
    factor_list = []
    if cfg.fa_name in factors_pkl:
        factor_list.append(cfg.fa_name)
    # if data_process_filter is not None:
    if isinstance(data_process_filter, tuple):
        _other_factor = f'factor_{data_process_filter[0]}_{data_process_filter[1]}'
        if _other_factor in factors_pkl:
            factor_list.append(_other_factor)
        else:
            raise ValueError(f"{_other_factor} 因子名输入有误")
    elif isinstance(data_process_filter, list):
        for _other_factor in data_process_filter:
            _other_factor = f'factor_{_other_factor[0]}_{_other_factor[1]}'
            if _other_factor in factors_pkl:
                factor_list.append(_other_factor)
            else:
                raise ValueError(f"{_other_factor} 因子名输入有误")
    elif isinstance(data_process_filter, dict):
        for v in data_process_filter.values():
            for _other_factor in v:
                if _other_factor in factors_pkl:
                    factor_list.append(_other_factor)
                else:
                    raise ValueError(f"{_other_factor} 因子名输入有误")
    elif isinstance(data_process_filter, str) and data_process_filter:
        _other_factor = data_process_filter
        if _other_factor in factors_pkl:
            factor_list.append(_other_factor)
        else:
            raise ValueError(f"{_other_factor} 因子名输入有误")
    # else:
    #     raise ValueError(f'{data_process_filter} 不支持的过滤因子格式')

    # 读取因子数据
    factor_df = tf.get_data(cfg, factor_list, boost)

    if isinstance(data_process_filter, dict):
        factor_df = data_process(factor_df, filter_list=list(data_process_filter.keys())[0])
    else:
        factor_df = data_process(factor_df, filter_list=data_process_filter)
    # logger.ok(factor_df.columns)
    # 存放图片的列表
    fig_list = []

    # ===计算因子的IC
    ic, ic_info, ic_month, ic_raw_info = tf.get_ic(factor_df, cfg)
    # 添加ic的曲线图
    fig_list.append(pf.draw_ic_plotly(x=ic['交易日期'], y1=ic['RankIC'], y2=ic['累计RankIC'], title='因子RankIC图',
                                      info=ic_info))
    # 添加阅读ic的热力图
    fig_list.append(pf.draw_hot_plotly(x=ic_month.columns, y=ic_month.index, z=ic_month,
                                       title='RankIC热力图(行：年份，列：月份)'))

    # ===计算因子的分组资金曲线及净值
    group_nv, group_value, group_hold_value = tf.get_group_net_value(factor_df, cfg)
    # 添加分组资金曲线图
    cols_list = [col for col in group_nv.columns if '第' in col]
    fig_list.append(pf.draw_line_plotly(x=group_nv['交易日期'], y1=group_nv[cols_list], y2=group_nv['多空净值'],
                                        if_log=True, title='分组资金曲线'))
    # 添加分组净值图
    fig_list.append(pf.draw_bar_plotly(x=group_value['分组'], y=group_value['净值'], title='分组净值'))
    # 添加分组持仓走势
    fig_list.append(pf.draw_line_plotly(x=group_hold_value['时间'], y1=group_hold_value[cols_list], update_xticks=True,
                                        if_log=False, title='分组持仓走势'))

    # ===计算因子的风格暴露
    style_corr = tf.get_style_corr(factor_df, cfg)
    # 添加风格暴露图
    fig_list.append(pf.draw_bar_plotly(x=style_corr['风格'], y=style_corr['相关系数'], title='因子风格暴露图',
                                       y_range=[-1.0, 1.0]))

    # ===计算行业平均IC以及行业占比
    industry_df = tf.get_class_ic_and_pct(factor_df, cfg)
    # 添加行业平均IC
    fig_list.append(pf.draw_bar_plotly(x=industry_df['新版申万一级行业名称'], y=industry_df['RankIC'],
                                       title='行业RankIC图'))
    # 添加行业占比图
    fig_list.append(pf.draw_double_bar_plotly(x=industry_df['新版申万一级行业名称'],
                                              y1=industry_df['因子第一组选股在各行业的占比'],
                                              y2=industry_df['因子最后一组选股在各行业的占比'],
                                              title='行业占比（可能会受到行业股票数量的影响）'))

    # ===计算不同市值分组内的平均IC以及市值占比
    market_df = tf.get_class_ic_and_pct(factor_df, cfg, is_industry=False)
    # 添加市值分组平均IC
    fig_list.append(pf.draw_bar_plotly(x=market_df['市值分组'], y=market_df['RankIC'], title='市值分组RankIC'))
    # 添加市值分组占比图
    info = '1-{bins}代表市值从小到大分{bins}组'.format(bins=cfg.bins)
    fig_list.append(pf.draw_double_bar_plotly(x=market_df['市值分组'], y1=market_df['因子第一组选股在各市值分组的占比'],
                                              y2=market_df['因子最后一组选股在各市值分组的占比'], title='市值占比',
                                              info=info))

    # ===计算因子得分
    score = tf.get_factor_score(ic, group_value)
    start_date = factor_df['交易日期'].min().strftime('%Y/%m/%d')
    end_date = factor_df['交易日期'].max().strftime('%Y/%m/%d')

    title = f'{cfg.fa_name}|{data_process_filter} 分析区间：{start_date} - {end_date}  分析周期：{cfg.period_offset}  因子得分：{score:.2f}'

    # ===整合上面所有的图
    save_path = tf.get_folder_path(cfg.get_analysis_folder(), '单因子分析')
    file_name = f'{cfg.fa_name}因子分析报告_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}'
    pf.merge_html(save_path, fig_list=fig_list, strategy_file=file_name,
                  bbs_id='31614', title=title, conf=cfg, show=False)
    # logger.ok((f'汇总数据并画图完成，耗时：{datetime.datetime.now() - start_time}')
    # logger.ok((f'{cfg.fa_name} 因子分析完成，耗时：{datetime.datetime.now() - start_time}')

    try:
        _, meta, param = name[0].split('_')
    except Exception as e:
        # logger.warning(f'factor: {name[0]} 不足3位')
        _, meta = name[0].split('_')
        param = ''
    tmp = group_value.iloc[:-1]
    tmp['1'] = np.arange(cfg.bins, 0, -1)
    tmp['单调性'] = tmp['1'].corr(tmp['净值'], method='spearman')
    tmp['分箱差'] = tmp['净值'].T.std()
    tmp['区分度'] = tmp['单调性'] * tmp['分箱差']
    # logger.ok(style_corr)
    r_data = [name[0], meta, param, data_process_filter, name[2], score] + \
             ic_raw_info + [factor_df[name[0]].isna().mean()] + \
             tmp[['单调性', '分箱差', '区分度']].iloc[-1].values.tolist() + \
             style_corr['相关系数'].tolist() + group_value['净值'].tolist() + \
              [str(name), save_path / f'{file_name}.html']
    # logger.ok(len(r_data), len(columns))
    # logger.ok(pd.DataFrame([r_data], columns=columns))
    return r_data


if __name__ == '__main__':
    logger.ok('开始运行因子分析程序...')
    all_factor_df_list = []
    pre_all_combinations, all_factor_list = gen_combinations()
    # if input('是否需要重新生成因子数据（Y/N）：') in ['Y', 'y']:
    #     factor_list = regen_cache(all_factor_list)

        # exit()
    # region =====需要配置的内容=====
    # 因子的名称，可以是数据中有的，按照运行缓存中的因子名输入，也可以是在data_process函数中计算出来的
    conf = load_config()
    conf.bins = 10  # 设置分组数量
    conf.limit = 100  # 设置每周期最少需要多少个股票
    conf.fee_rate = (1 - conf.c_rate) * (1 - conf.c_rate - conf.t_rate)  # 提前计算好手续费的比例


    columns = ['因子', '元因子', '参数', '分域', '周期', '因子得分',
               'IC方向', 'IC均值', 'IC标准差', 'IC累计', 'ICIR', 'IC胜率', 'IC单调性',
               'IC近2年单调性', 'IC近1年单调性', 'IC近半年单调性',
               '因子空值率',
               '分组单调性', '分箱差', '区分度',
               '估值', '动量', '反转', '成长', '杠杆', '波动', '盈利', '规模'] + [
                f'第{i+1}组' for i in range(conf.bins)] + ['多空净值', '校验码', '报告地址']

    file_name = os.path.join('..', 'data', '分析结果',
                             f'因子分析报告_{datetime.datetime.now().strftime("%Y_%m_%d")}.xlsx')  # 分析报告，每天一份
    if os.path.exists(file_name):
        tmp = pd.read_excel(file_name, engine='openpyxl')['校验码'].to_list()
    else:
        tmp = pd.DataFrame(columns=columns)

    all_combinations = list(set([str(x) for x in pre_all_combinations]) -  set(tmp))[:]

    print(f'已完成：{len(tmp)}\n{len(all_combinations)}（需要计算） = {len(pre_all_combinations)}（原计划） - {len(pre_all_combinations) - len(all_combinations)}（重复）')

    # factor_analysis(all_combinations[1], data_process, conf, True)
    # exit()

    for i in tqdm(range(0, len(all_combinations), 50), desc='分批进度'):
        test_combinations = all_combinations[i:i + 50]
        result_list = []

        with ProcessPoolExecutor(max_workers=10 ) as executor:
            futures = [executor.submit(factor_analysis, combo, data_process, conf, True) for combo
                       in test_combinations[:]]
            #
            #
            for future in tqdm(as_completed(futures), total=len(test_combinations), desc='🧮 因子分析'):
                try:
                    result_list.append(future.result())
                    all_factor_df_list.extend(result_list)
                except Exception as e:
                    # error_message = traceback.format_exc()
                    logger.error(f'因子分析时错误： {e}')
                    # raise ValueError(e)
        # print(columns, all_factor_df_list)
        all_factors_df = pd.DataFrame(result_list, columns=columns)
        if os.path.exists(file_name):
            tmp = pd.read_excel(file_name, engine='openpyxl') # 读取历史，与新的记录合并
            tmp['因子'] = [f'factor_{x}_{y}' for x, y in zip(tmp['元因子'], tmp['参数'])] # 读取时hyperlink信息会丢失，重建一下
            if not tmp.empty:
                all_factors_df = pd.concat([all_factors_df, tmp], ignore_index=True)
        try:
            all_factors_df['因子'] = all_factors_df.apply(
                lambda row: f'=HYPERLINK("{row["报告地址"]}", "{row["因子"]}")',
                axis=1
            )
            with pd.ExcelWriter(file_name, engine='openpyxl', mode='w') as writer:
                all_factors_df.to_excel(writer, index=False)  # 保存结果
        except Exception as e:
            logger.warning(f'{e}\n解决后请按任意键继续')
            input()
            with pd.ExcelWriter(file_name, engine='openpyxl', mode='w') as writer:
                all_factors_df.to_excel(writer, index=False)  # 保存结果
        # logger.ok(f'结果已保存至：  file:///{quote(file_name.replace(os.path.sep, "/"))}')  # 输出到terminal，点一下就可以打开，方便懒人吧
