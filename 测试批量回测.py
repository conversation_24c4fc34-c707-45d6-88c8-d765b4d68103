#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试批量回测功能

验证修复后的回测主程序是否能正常工作
"""

import warnings
import pandas as pd
from pathlib import Path

warnings.filterwarnings('ignore')
pd.set_option('expand_frame_repr', False)
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)

def test_strategy_loading():
    """测试策略加载功能"""
    print("🧪 测试策略加载功能")
    print("=" * 60)
    
    # 导入修复后的函数
    try:
        from 回测主程序 import load_all_strategies
        
        # 测试加载策略
        strategies = load_all_strategies()
        
        if strategies:
            print(f"\n📋 加载的策略列表:")
            for i, (name, config) in enumerate(strategies.items(), 1):
                print(f"{i}. {name}")
                print(f"   策略名称: {config.get('name', 'N/A')}")
                print(f"   持仓周期: {config.get('hold_period', 'N/A')}")
                print(f"   选股数量: {config.get('select_num', 'N/A')}")
                print(f"   因子数量: {len(config.get('factor_list', []))}")
                print(f"   过滤条件: {len(config.get('filter_list', []))}个")
                
                # 检查因子配置
                factor_list = config.get('factor_list', [])
                if factor_list:
                    print(f"   因子详情:")
                    for j, factor in enumerate(factor_list[:3], 1):  # 只显示前3个
                        if isinstance(factor, (list, tuple)) and len(factor) >= 2:
                            factor_name = factor[0]
                            factor_param = factor[2] if len(factor) > 2 else 'None'
                            print(f"     {j}. {factor_name} (参数: {factor_param})")
                        else:
                            print(f"     {j}. {factor}")
                    if len(factor_list) > 3:
                        print(f"     ... 还有 {len(factor_list) - 3} 个因子")
                print()
            
            return True
        else:
            print("❌ 没有加载到任何策略")
            return False
            
    except Exception as e:
        print(f"❌ 策略加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载功能"""
    print("\n🧪 测试配置加载功能")
    print("=" * 60)
    
    try:
        from core.model.backtest_config import load_config
        
        conf = load_config()
        print(f"✅ 配置加载成功")
        print(f"   配置类型: {type(conf)}")
        print(f"   配置属性: {[attr for attr in dir(conf) if not attr.startswith('_')][:10]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_strategy_format():
    """测试策略格式是否正确"""
    print("\n🧪 测试策略格式")
    print("=" * 60)
    
    try:
        from 回测主程序 import load_all_strategies
        
        strategies = load_all_strategies()
        
        required_fields = ['name', 'hold_period', 'offset_list', 'select_num', 'factor_list']
        
        for strategy_name, config in strategies.items():
            print(f"\n🔍 检查策略: {strategy_name}")
            
            # 检查必需字段
            missing_fields = []
            for field in required_fields:
                if field not in config:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ 缺少字段: {missing_fields}")
            else:
                print(f"   ✅ 必需字段完整")
            
            # 检查因子格式
            factor_list = config.get('factor_list', [])
            if factor_list:
                print(f"   📊 因子格式检查:")
                for i, factor in enumerate(factor_list[:3]):
                    if isinstance(factor, (list, tuple)):
                        if len(factor) >= 4:  # (name, is_asc, param, weight)
                            print(f"     ✅ 因子{i+1}: 格式正确 {factor[0]}")
                        else:
                            print(f"     ⚠️ 因子{i+1}: 格式可能不完整 {factor}")
                    else:
                        print(f"     ❌ 因子{i+1}: 格式错误 {factor}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略格式测试失败: {e}")
        return False

def check_震荡修复策略():
    """检查震荡修复策略是否正确加载"""
    print("\n🧪 检查震荡修复策略")
    print("=" * 60)
    
    try:
        # 检查震荡修复策略文件
        strategy_file = Path('实盘/震荡修复动量策略.py')
        if not strategy_file.exists():
            print(f"❌ 策略文件不存在: {strategy_file}")
            return False
        
        # 尝试导入
        import importlib
        module = importlib.import_module('实盘.震荡修复动量策略')
        
        if hasattr(module, 'strategy_list'):
            strategy_list = module.strategy_list
            print(f"✅ 找到strategy_list，类型: {type(strategy_list)}")
            
            if isinstance(strategy_list, list) and len(strategy_list) > 0:
                first_strategy = strategy_list[0]
                print(f"✅ 第一个策略类型: {type(first_strategy)}")
                
                if isinstance(first_strategy, dict):
                    print(f"✅ 策略配置正确")
                    print(f"   策略名称: {first_strategy.get('name', 'N/A')}")
                    
                    # 检查因子配置
                    factor_list = first_strategy.get('factor_list', [])
                    for factor in factor_list:
                        if isinstance(factor, (list, tuple)) and len(factor) >= 3:
                            factor_name, is_asc, param = factor[0], factor[1], factor[2]
                            if factor_name == '跌停起步改' and isinstance(param, tuple) and len(param) == 5:
                                print(f"   ✅ 震荡修复因子配置正确: {param}")
                            else:
                                print(f"   📊 其他因子: {factor_name}")
                    
                    return True
                else:
                    print(f"❌ 策略配置不是字典格式")
            else:
                print(f"❌ strategy_list为空或不是列表")
        else:
            print(f"❌ 模块没有strategy_list属性")
        
        return False
        
    except Exception as e:
        print(f"❌ 震荡修复策略检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_single_backtest():
    """模拟单个策略回测（不实际运行）"""
    print("\n🧪 模拟单个策略回测")
    print("=" * 60)
    
    try:
        from core.model.backtest_config import load_config
        from 回测主程序 import load_all_strategies
        
        # 加载配置和策略
        conf = load_config()
        strategies = load_all_strategies()
        
        if not strategies:
            print("❌ 没有可用的策略")
            return False
        
        # 选择第一个策略进行模拟
        strategy_name, strategy_config = next(iter(strategies.items()))
        
        print(f"📋 模拟回测策略: {strategy_name}")
        print(f"   策略名称: {strategy_config.get('name', 'N/A')}")
        
        # 设置配置
        conf.name = strategy_config.get('name', strategy_name)
        conf.strategy_list = [strategy_config]
        
        print(f"✅ 配置设置成功")
        print(f"   回测名称: {conf.name}")
        print(f"   策略数量: {len(conf.strategy_list)}")
        
        # 这里不实际运行回测，只是验证配置
        print(f"✅ 模拟回测配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 批量回测功能测试套件")
    print("=" * 80)
    
    tests = [
        ("策略加载测试", test_strategy_loading),
        ("配置加载测试", test_config_loading),
        ("策略格式测试", test_strategy_format),
        ("震荡修复策略检查", check_震荡修复策略),
        ("模拟回测测试", simulate_single_backtest),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print(f"\n{'='*80}")
    print("📊 测试结果总结")
    print(f"{'='*80}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！批量回测功能应该可以正常工作。")
        print("\n💡 建议:")
        print("1. 可以运行 python 回测主程序.py 进行实际批量回测")
        print("2. 确保因子库中的'跌停起步改'因子文件存在且正确")
        print("3. 检查数据文件是否完整")
    else:
        print("⚠️ 部分测试失败，请检查相关问题后再运行批量回测。")

if __name__ == '__main__':
    main()
