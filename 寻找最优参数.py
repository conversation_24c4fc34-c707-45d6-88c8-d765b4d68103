"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import datetime
import os.path
import random
import shutil
import time
import warnings

import pandas as pd
from tqdm import tqdm
from core.backtest import run_backtest_multi
from core.model.backtest_config import create_factory
from core.version import version_prompt
import itertools
# ====================================================================================================
# ** 脚本运行前配置 **
# 主要是解决各种各样奇怪的问题们
# ====================================================================================================
warnings.filterwarnings('ignore')  # 过滤一下warnings，不要吓到老实人

# pandas相关的显示设置，基础课程都有介绍
pd.set_option('expand_frame_repr', False)  # 当列太多时不换行
pd.set_option('display.unicode.ambiguous_as_wide', True)  # 设置命令行输出时的列对齐功能
pd.set_option('display.unicode.east_asian_width', True)

def dict_itertools(dict_):
    keys = list(dict_.keys())
    values = list(dict_.values())
    return [dict(zip(keys, combo)) for combo in itertools.product(*values)]

if __name__ == '__main__':
    # version_prompt()
    print(f'🌀 系统启动中，稍等...')
    r_time = time.time()
    # ====================================================================================================
    # 1. 配置需要遍历的参数
    # ====================================================================================================
    # 因子遍历的参数范围
    strategies = []

    # p = 0.1
    from 信号库.all_timings import timmings
    param_dict = {
        # 'filter_list': [
        #     ('alpha95', 20, f'pct:<={p}', False),
        #     ('SCR', [20, 90], f'pct:<={p}', False),
        #     ('Ret', 60, f'pct:<={p}', False),
        #     ('归母净利润同比增速', 60, f'pct:<={p}', False),
        #     ('成交额缩波因子', (13, 90), f'pct:<={p}', False),
        #     ('异常涨跌幅', (90, 2),f'pct:<={p}', False),
        #     ('成交额STD', 5, f'pct:<={p}', True),
        #     ('月份', [1], 'val:!=1'),
        #     ('月份', [4], 'val:!=1'),
        # ],
        # 'hold_period': ['W:5', '5:5', '3:3', '2:2'],
        # 'n': list(range(1, 5)),
        # '最大回撤': [3, 21, 90, 120, 250],
        # 'Ret': [3, 21, 90, 120, 250],
        'p': [1.03, 1.05, 1.1],
        'm': [3, 5, 8, 13, 21, 30],
        'n': range(1, 5),
        'ratio': [0.3, 0.5, 1, 2, 3]
        # 'x': [3, 5, 8, 13, 21, 30, 60, 90, 120, 250],
        # 'm': range(3),
        # 'time': ['0945', '0955', '1000', '1005'],
        # 'stock_nums': [2, 3, 5, 10, 30],
        # '筹码集中度': [x/20 for x in range(20)],
        # '归母': [x/20 for x in range(20)],
        # '归母_n': [x*30 for x in range(2, 9)],
        # 'timmings': list(timmings.values()),
        # 'n': [0, 0.1, 0.2, 0.5, 100, 200, 500, 1000, 2000],
        # 'p': [0.2, 0.5, 0.7, 0.8],
        # 'op': ['<=', '>='],
        # 'cm': [
        #     '筹码集中度', '筹码偏离度', '胜率', '上方筹码集中度', '下方筹码集中度',
        #     '筹码穿透率', '主力筹码控盘率', '筹码获利比例', '筹码成本区间',
        #     '筹码波动率', '短期筹码变动', '中期筹码变动', '长期筹码变动'
        # ],
        # 'filter_cm': [
            # ('筹码相关因子', '筹码集中度', 'pct:>=0.7', True),
            # ('筹码相关因子', '长期筹码变动', 'pct:<=0.2', True),
            # ('筹码相关因子', '筹码波动率', 'pct:>=0.5', True),
            # ('筹码相关因子', '筹码成本区间', 'pct:>=0.8', True),
        # ],
    }

    # all_strageties = """
    # 筹码基本面.py
    # 筹码小市值_下方筹码集中度.py
    # 筹码小市值_中期筹码变动.py
    # 筹码小市值_主力筹码控盘率.py
    # 筹码小市值_短期筹码变动.py
    # 筹码小市值_筹码偏离度.py
    # 筹码小市值_筹码成本区间.py
    # 筹码小市值_筹码波动率.py
    # 筹码小市值_筹码穿透率.py
    # 筹码小市值_筹码获利比例.py
    # 筹码小市值_筹码集中度.py
    # 筹码小市值_胜率.py
    # 筹码小市值_长期筹码变动.py
    # """
    #
    # strategies_list = [x[:-3] for x in all_strageties.split() if '#' not in x]
    #
    # import importlib
    #
    # strategy_list = []
    # for stg in strategies_list:
    #     strategy_module = importlib.import_module('实盘.' + stg)
    #     strategy_list.append(strategy_module.strategy_list[0]['filter_list'][0])
    #
    # for n in range(2, 4):
    #     param_dict['filter_cm'] += list(itertools.combinations(strategy_list, n))
    #
    # print(len(param_dict['filter_cm']), param_dict['filter_cm'][-3:])
    # exit()

    # all_params = []
    # for n in range(1, 4):  # 组合大小从1到3
    #     for p in range(1, 10):  # p 的值从1到9
    #         # 动态填充 p 值
    #         filled_filters = []
    #         for condition in param_dict['filter_list']:
    #             if isinstance(condition[2], str) and '<=' in condition[2]:
    #                 filled_condition = (condition[0], condition[1], condition[2].replace('0.1', str(p/10)), condition[3])
    #                 filled_filters.append(filled_condition)
    #             else:
    #                 filled_filters.append(condition)
    #
    #         # 生成组合
    #         for filter_combination in itertools.combinations(filled_filters, n):
    #             all_params.append(list(filter_combination))
    #
    # print(random.choices(all_params, k

    backtest_name = '小市值_动量恢复'
    for param in dict_itertools(param_dict):
        n, m, p, ratio =  param['n'], param['m'], param['p'], param['ratio']#, param['x']#, param['Ret'], param['filter_cm']
        # hold, offset = hold.split(':')
        # for off in range(int(offset)):
    # for x in all_params:
        strategy_list = [
            {
                'name': backtest_name,
                'hold_period': 'W',
                'offset_list': [0,1,2,3,4],
                'select_num': 5,
                'cap_weight': 1,
                'rebalance_time': '0955-0955',
                'factor_list': [
                    # ('Ret', False, 5, 100),

                    ('跌停起步改', False, (n, m, p), ratio),
                    # ('Ret', False, 21, 0.2),
                    # ('一级行业', False, None, 1),
                    ('市值', True, None, 1),
                                ],
                'filter_list': [
                    # ('交易所', 'bj', "val:!=1"),
                    # ('交易所', 'kcb', "val:!=1"),
                    # ('成交额Mean', 5, 'val:>=5000_0000', True)
                ],

                'timing': {
                    'name': '定风波1P5择时',  # 择时策略名称
                    'limit': 0.8,
                    'factor_list': [('开盘至今涨幅', False, '0945', 1, '0945'),
                                    ('隔夜涨跌幅', False, '开盘价', 1, '开盘价'),
                                    # ('开盘至今涨幅', False, None, 1, '0945'),
                                    # ('隔夜涨跌幅', False, None, 1, '0935'),
                                    # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                                    # ('次日涨跌停状态', False, '跌停', 1, '0945')
                                    ],
                    'params': 0.85
                }
            }
        ]
        strategies.append(strategy_list)

    print(len(strategies), strategies[-1])
    # exit()
    # ====================================================================================================
    # 2. 生成策略配置
    # ====================================================================================================
    print(f'🌀 生成策略配置...')

    num = 100
    for i in tqdm(range(0, len(strategies), num)):
        backtest_factory = create_factory(strategies[i : i+num])

    # ====================================================================================================
    # 3. 寻找最优参数
    # ====================================================================================================
    # boost为True：并行选股；boost为False：串行选股
    # 第一次运行，且不太确定的时候，可以考虑使用 `boost=False`，回测组不多的时候，不会慢太多的哈~
        report_list = run_backtest_multi(backtest_factory, boost=True, del_cache=False)

        # ====================================================================================================
        # 4. 根据回测参数列表，展示最优参数
        # ====================================================================================================
        s_time = time.time()
        print(f'🌀 展示最优参数...')
        all_params_map = pd.concat(report_list, ignore_index=True)
        report_columns = all_params_map.columns  # 缓存列名

        # 合并参数细节
        sheet = backtest_factory.get_name_params_sheet()
        all_params_map = all_params_map.merge(sheet, left_on='param', right_on='策略详情', how='left')

        # print(all_params_map)
        all_params_map = all_params_map[[*sheet.columns, *report_columns]].drop(columns=['param'])
        file_name = backtest_factory.result_folder / f'最优参数_{backtest_name}_{datetime.datetime.now().strftime("%Y%m%d")}.xlsx'
        # 检查文件是否存在
        if os.path.exists(file_name):
            # 读取现有数据
            pre_data = pd.read_excel(file_name, engine='openpyxl')
            # 合并数据
            all_params_map = pd.concat([all_params_map, pre_data], ignore_index=True)

        # 保存为 Excel 文件
        try:
            with pd.ExcelWriter(file_name, engine='openpyxl', mode='w') as writer:
                all_params_map.to_excel(writer, index=False)
        except Exception as e:
            print('写入失败：', e, '准备好后回车继续')
            input()


        print(all_params_map.sort_values(by='累积净值', ascending=False).drop_duplicates()['累积净值 年化收益 最大回撤 年化收益/回撤比 胜率（含0/去0）'.split()].head(20))
        print(f'✅ 完成展示最优参数，花费时间：{time.time() - s_time:.2f}秒，累计时间：{(time.time() - r_time):.3f}秒')
        print(f'文档位置： {file_name}')

        all_dirs = [x for x in os.listdir(backtest_factory.result_folder) if os.path.isdir(os.path.join(backtest_factory.result_folder, x))]
        for fdir in all_dirs:
            shutil.rmtree(os.path.join(backtest_factory.result_folder, fdir), ignore_errors=True)

