"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行

因子说明：股价恢复速度动量因子
基于跌停后股价恢复速度构建的动量类因子
参数说明：
- method=1: 基于恢复天数的动量评分
- method=2: 基于恢复幅度的动量评分
- method=3: 基于恢复速度的动量评分
- method=4: 综合恢复动量评分
"""
import pandas as pd
import numpy as np
from numba import jit
import warnings
warnings.filterwarnings('ignore')

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> pd.DataFrame:
    """
    股价恢复速度动量因子

    参数说明：
    param: (method, lookback_days, recovery_threshold)
    - method: 计算方法 (1-4)
    - lookback_days: 回看天数，用于寻找跌停事件
    - recovery_threshold: 恢复阈值，如1.05表示恢复5%
    """
    # ======================== 参数处理 ===========================
    col_name = kwargs['col_name']

    if param is None:
        method, lookback_days, recovery_threshold = 1, 20, 1.05
    else:
        method, lookback_days, recovery_threshold = param

    # ======================== 计算因子 ===========================
    # 初始化结果列
    df[col_name] = 0.0

    # 识别跌停事件
    df['是否跌停'] = 0
    df.loc[df['收盘价'] == df['跌停价'], '是否跌停'] = 1

    # 计算不同方法的动量因子
    if method == 1:
        # 方法1: 基于恢复天数的动量评分
        df[col_name] = _calculate_recovery_days_momentum(df, lookback_days, recovery_threshold)
    elif method == 2:
        # 方法2: 基于恢复幅度的动量评分
        df[col_name] = _calculate_recovery_magnitude_momentum(df, lookback_days, recovery_threshold)
    elif method == 3:
        # 方法3: 基于恢复速度的动量评分
        df[col_name] = _calculate_recovery_speed_momentum(df, lookback_days, recovery_threshold)
    elif method == 4:
        # 方法4: 综合恢复动量评分
        df[col_name] = _calculate_comprehensive_momentum(df, lookback_days, recovery_threshold)

    # 处理异常值和缺失值
    df[col_name] = df[col_name].fillna(0)
    df[col_name] = np.clip(df[col_name], -3, 3)  # 限制在[-3, 3]范围内

    return df[[col_name]]


@jit(nopython=True)
def _calculate_recovery_days_numba(prices, limit_down_positions, recovery_threshold, lookback_days):
    """
    使用Numba加速的恢复天数计算
    """
    n = len(prices)
    momentum = np.zeros(n)

    for limit_pos in limit_down_positions:
        if limit_pos >= n - 1:
            continue

        limit_price = prices[limit_pos]
        target_price = limit_price * recovery_threshold

        # 查找恢复点
        recovery_day = 0
        end_pos = min(limit_pos + lookback_days + 1, n)

        for i in range(limit_pos + 1, end_pos):
            if prices[i] >= target_price:
                recovery_day = i - limit_pos
                break

        if recovery_day > 0:
            score = max(0.0, 2.0 - recovery_day / 5.0)
            # 赋值给恢复点之后的所有点
            score_end = min(limit_pos + recovery_day + lookback_days, n)
            for j in range(limit_pos + recovery_day, score_end):
                momentum[j] = score

    return momentum


def _calculate_recovery_days_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法1: 基于恢复天数的动量评分（最终优化版）
    恢复越快，动量评分越高
    """
    # 预处理数据
    prices = df['收盘价_复权'].values
    limit_down_mask = (df['是否跌停'] == 1).values
    limit_down_positions = np.where(limit_down_mask)[0]

    if len(limit_down_positions) == 0:
        return pd.Series(0.0, index=df.index)

    # 使用Numba加速计算
    momentum_values = _calculate_recovery_days_numba(
        prices, limit_down_positions, recovery_threshold, lookback_days
    )

    return pd.Series(momentum_values, index=df.index)


@jit(nopython=True)
def _calculate_recovery_magnitude_numba(prices, limit_down_positions, lookback_days):
    """
    使用Numba加速的恢复幅度计算
    """
    n = len(prices)
    momentum = np.zeros(n)

    for limit_pos in limit_down_positions:
        if limit_pos >= n - 1:
            continue

        limit_price = prices[limit_pos]
        end_pos = min(limit_pos + lookback_days + 1, n)

        for i in range(limit_pos + 1, end_pos):
            if prices[i] > limit_price:
                recovery_ratio = (prices[i] / limit_price - 1) * 100
                score = min(2.0, recovery_ratio / 10.0)
                momentum[i] = score

    return momentum


def _calculate_recovery_magnitude_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法2: 基于恢复幅度的动量评分（最终优化版）
    从跌停价恢复的幅度越大，动量评分越高
    """
    # 预处理数据
    prices = df['收盘价_复权'].values
    limit_down_mask = (df['是否跌停'] == 1).values
    limit_down_positions = np.where(limit_down_mask)[0]

    if len(limit_down_positions) == 0:
        return pd.Series(0.0, index=df.index)

    # 使用Numba加速计算
    momentum_values = _calculate_recovery_magnitude_numba(
        prices, limit_down_positions, lookback_days
    )

    return pd.Series(momentum_values, index=df.index)


@jit(nopython=True)
def _calculate_recovery_speed_numba(prices, limit_down_positions, lookback_days):
    """
    使用Numba加速的恢复速度计算
    """
    n = len(prices)
    momentum = np.zeros(n)

    for limit_pos in limit_down_positions:
        if limit_pos >= n - 1:
            continue

        limit_price = prices[limit_pos]
        end_pos = min(limit_pos + lookback_days + 1, n)

        for i in range(limit_pos + 1, end_pos):
            if prices[i] > limit_price:
                recovery_days = i - limit_pos
                recovery_ratio = (prices[i] / limit_price - 1) * 100
                recovery_speed = recovery_ratio / recovery_days
                score = min(2.0, recovery_speed / 2.0)
                momentum[i] = score

    return momentum


def _calculate_recovery_speed_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法3: 基于恢复速度的动量评分（最终优化版）
    恢复速度 = 恢复幅度 / 恢复天数
    """
    # 预处理数据
    prices = df['收盘价_复权'].values
    limit_down_mask = (df['是否跌停'] == 1).values
    limit_down_positions = np.where(limit_down_mask)[0]

    if len(limit_down_positions) == 0:
        return pd.Series(0.0, index=df.index)

    # 使用Numba加速计算
    momentum_values = _calculate_recovery_speed_numba(
        prices, limit_down_positions, lookback_days
    )

    return pd.Series(momentum_values, index=df.index)


@jit(nopython=True)
def _calculate_comprehensive_numba(prices, limit_down_positions, lookback_days, recovery_threshold):
    """
    使用Numba加速的综合动量计算
    """
    n = len(prices)
    days_momentum = np.zeros(n)
    magnitude_momentum = np.zeros(n)
    speed_momentum = np.zeros(n)

    for limit_pos in limit_down_positions:
        if limit_pos >= n - 1:
            continue

        limit_price = prices[limit_pos]
        target_price = limit_price * recovery_threshold
        end_pos = min(limit_pos + lookback_days + 1, n)

        # 计算恢复天数动量
        recovery_day = 0
        for i in range(limit_pos + 1, end_pos):
            if prices[i] >= target_price:
                recovery_day = i - limit_pos
                break

        if recovery_day > 0:
            score = max(0.0, 2.0 - recovery_day / 5.0)
            score_end = min(limit_pos + recovery_day + lookback_days, n)
            for j in range(limit_pos + recovery_day, score_end):
                days_momentum[j] = score

        # 计算恢复幅度和速度动量
        for i in range(limit_pos + 1, end_pos):
            if prices[i] > limit_price:
                # 恢复幅度
                recovery_ratio = (prices[i] / limit_price - 1) * 100
                magnitude_score = min(2.0, recovery_ratio / 10.0)
                magnitude_momentum[i] = magnitude_score

                # 恢复速度
                recovery_days = i - limit_pos
                recovery_speed = recovery_ratio / recovery_days
                speed_score = min(2.0, recovery_speed / 2.0)
                speed_momentum[i] = speed_score

    # 加权综合
    comprehensive = (
        0.3 * days_momentum +
        0.3 * magnitude_momentum +
        0.4 * speed_momentum
    )

    return comprehensive


def _calculate_comprehensive_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法4: 综合恢复动量评分（最终优化版）
    结合恢复天数、恢复幅度和恢复速度的综合评分
    """
    # 预处理数据
    prices = df['收盘价_复权'].values
    limit_down_mask = (df['是否跌停'] == 1).values
    limit_down_positions = np.where(limit_down_mask)[0]

    if len(limit_down_positions) == 0:
        return pd.Series(0.0, index=df.index)

    # 使用Numba加速计算
    momentum_values = _calculate_comprehensive_numba(
        prices, limit_down_positions, lookback_days, recovery_threshold
    )

    return pd.Series(momentum_values, index=df.index)