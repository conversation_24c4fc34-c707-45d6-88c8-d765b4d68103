"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import pandas as pd

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> pd.DataFrame:
    # ======================== 参数处理 ===========================
    col_name = kwargs['col_name']

    n, m = param

    # ======================== 计算因子 ===========================
    # 初始化异常状态列
    # df[col_name] = 0
    # 收盘价等于跌停价时标记为1
    df.loc[df['收盘价'] == df['跌停价'], '是否跌停'] = 1

    # 新增一列用于保存跌停的收盘价，默认填充NaN或您可以选择的其他值（如0）
    limit_down_col = '最近跌停收盘价'  # 指定新列的名称
    df[limit_down_col] = None  # 或者 df[limit_down_col] = 0

    # 将跌停的收盘价赋值到新列中
    df.loc[df['是否跌停'] == 1, limit_down_col] = df.loc[df['是否跌停'] == 1, '收盘价_复权']

    # 填充缺失值为0（确保无NaN）
    df['是否跌停'].fillna(0, inplace=True)
    df[limit_down_col].fillna(method='ffill', inplace=True)
    df[limit_down_col].fillna(0, inplace=True)
    df['最近跌停'] = df['是否跌停'].rolling(window=n).sum()

    df.loc[(df['最近跌停'] > m) & (df['收盘价_复权'] > df[limit_down_col]), col_name] = 1

    df[col_name].fillna(0, inplace=True)

    return df[[col_name]]  # 返回包含新增列的结果