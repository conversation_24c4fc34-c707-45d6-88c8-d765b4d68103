"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行

因子说明：股价恢复速度动量因子
基于跌停后股价恢复速度构建的动量类因子
参数说明：
- method=1: 基于恢复天数的动量评分
- method=2: 基于恢复幅度的动量评分
- method=3: 基于恢复速度的动量评分
- method=4: 综合恢复动量评分
"""
import pandas as pd
import numpy as np

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> pd.DataFrame:
    """
    股价恢复速度动量因子

    参数说明：
    param: (method, lookback_days, recovery_threshold)
    - method: 计算方法 (1-4)
    - lookback_days: 回看天数，用于寻找跌停事件
    - recovery_threshold: 恢复阈值，如1.05表示恢复5%
    """
    # ======================== 参数处理 ===========================
    col_name = kwargs['col_name']

    if param is None:
        method, lookback_days, recovery_threshold = 1, 20, 1.05
    else:
        method, lookback_days, recovery_threshold = param

    # ======================== 计算因子 ===========================
    # 初始化结果列
    df[col_name] = 0.0

    # 识别跌停事件
    df['是否跌停'] = 0
    df.loc[df['收盘价'] == df['跌停价'], '是否跌停'] = 1

    # 计算不同方法的动量因子
    if method == 1:
        # 方法1: 基于恢复天数的动量评分
        df[col_name] = _calculate_recovery_days_momentum(df, lookback_days, recovery_threshold)
    elif method == 2:
        # 方法2: 基于恢复幅度的动量评分
        df[col_name] = _calculate_recovery_magnitude_momentum(df, lookback_days, recovery_threshold)
    elif method == 3:
        # 方法3: 基于恢复速度的动量评分
        df[col_name] = _calculate_recovery_speed_momentum(df, lookback_days, recovery_threshold)
    elif method == 4:
        # 方法4: 综合恢复动量评分
        df[col_name] = _calculate_comprehensive_momentum(df, lookback_days, recovery_threshold)

    # 处理异常值和缺失值
    df[col_name] = df[col_name].fillna(0)
    df[col_name] = np.clip(df[col_name], -3, 3)  # 限制在[-3, 3]范围内

    return df[[col_name]]


def _calculate_recovery_days_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法1: 基于恢复天数的动量评分
    恢复越快，动量评分越高
    """
    momentum = pd.Series(0.0, index=df.index)

    for i in range(lookback_days, len(df)):
        # 寻找最近的跌停事件
        recent_limit_down_idx = None
        for j in range(i - lookback_days, i):
            if df.iloc[j]['是否跌停'] == 1:
                recent_limit_down_idx = j
                break

        if recent_limit_down_idx is not None:
            limit_down_price = df.iloc[recent_limit_down_idx]['收盘价_复权']
            target_price = limit_down_price * recovery_threshold

            # 计算恢复天数
            recovery_days = 0
            for k in range(recent_limit_down_idx + 1, i + 1):
                if df.iloc[k]['收盘价_复权'] >= target_price:
                    recovery_days = k - recent_limit_down_idx
                    break

            if recovery_days > 0:
                # 恢复天数越少，动量评分越高
                momentum.iloc[i] = max(0, 2.0 - recovery_days / 5.0)

    return momentum


def _calculate_recovery_magnitude_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法2: 基于恢复幅度的动量评分
    从跌停价恢复的幅度越大，动量评分越高
    """
    momentum = pd.Series(0.0, index=df.index)

    for i in range(lookback_days, len(df)):
        # 寻找最近的跌停事件
        recent_limit_down_idx = None
        for j in range(i - lookback_days, i):
            if df.iloc[j]['是否跌停'] == 1:
                recent_limit_down_idx = j
                break

        if recent_limit_down_idx is not None:
            limit_down_price = df.iloc[recent_limit_down_idx]['收盘价_复权']
            current_price = df.iloc[i]['收盘价_复权']

            if current_price > limit_down_price:
                # 计算恢复幅度
                recovery_ratio = (current_price / limit_down_price - 1) * 100
                # 恢复幅度转换为动量评分
                momentum.iloc[i] = min(2.0, recovery_ratio / 10.0)  # 每10%恢复对应1分

    return momentum


def _calculate_recovery_speed_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法3: 基于恢复速度的动量评分
    恢复速度 = 恢复幅度 / 恢复天数
    """
    momentum = pd.Series(0.0, index=df.index)

    for i in range(lookback_days, len(df)):
        # 寻找最近的跌停事件
        recent_limit_down_idx = None
        for j in range(i - lookback_days, i):
            if df.iloc[j]['是否跌停'] == 1:
                recent_limit_down_idx = j
                break

        if recent_limit_down_idx is not None:
            limit_down_price = df.iloc[recent_limit_down_idx]['收盘价_复权']
            current_price = df.iloc[i]['收盘价_复权']
            recovery_days = i - recent_limit_down_idx

            if current_price > limit_down_price and recovery_days > 0:
                # 计算恢复速度
                recovery_ratio = (current_price / limit_down_price - 1) * 100
                recovery_speed = recovery_ratio / recovery_days
                # 速度转换为动量评分
                momentum.iloc[i] = min(2.0, recovery_speed / 2.0)  # 每日2%恢复对应1分

    return momentum


def _calculate_comprehensive_momentum(df: pd.DataFrame, lookback_days: int, recovery_threshold: float) -> pd.Series:
    """
    方法4: 综合恢复动量评分
    结合恢复天数、恢复幅度和恢复速度的综合评分
    """
    # 获取各个子因子
    days_momentum = _calculate_recovery_days_momentum(df, lookback_days, recovery_threshold)
    magnitude_momentum = _calculate_recovery_magnitude_momentum(df, lookback_days, recovery_threshold)
    speed_momentum = _calculate_recovery_speed_momentum(df, lookback_days, recovery_threshold)

    # 加权综合
    comprehensive_momentum = (
        0.3 * days_momentum +
        0.3 * magnitude_momentum +
        0.4 * speed_momentum
    )

    return comprehensive_momentum