#%%
from idlelib.iomenu import encoding

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')
#%%
path = r'D:\projects\邢大小组\select-stock-pro_v1.1.0\data\运行缓存2'
df = pd.read_pickle(f'{path}\\all_factors_kline.pkl')
df['pct'] = df.groupby('股票代码')['收盘价'].pct_change()
df['pct3d'] = df['pct'].shift(-3)
df['pct5d'] = df['pct'].shift(-5)
# df.sample(4)
#%%
for n in range(1, 5):
    for m in [3, 5, 8, 13, 21, 30]:
        for p in [1.03, 1.05, 1.1]:
            factor_name = f'factor_跌停起步改_({n},{m},{p}).pkl'
            df_factor = pd.read_pickle(f'{path}\\{factor_name}')
            df_tmp = pd.concat([df, df_factor], axis=1)
            # print(df_tmp.sample(4))
            cor = df_tmp[['pct', 'pct3d', 'pct5d', factor_name[7:-4]]].corr()
            # print(cor)
            print(factor_name[7:-4], cor.iloc[-1].tolist())
            # exit()
# factor_name = 'factor_Ret_21.pkl'
# df_factor = pd.read_pickle(f'{path}\\{factor_name}')
# df = pd.concat([df, df_factor], axis=1)
# df[['pct', 'pct3d', 'pct5d', factor_name[7:-4]]].corr()
#%%
zlm_df = pd.read_pickle(r'D:\projects\邢大小组\select-stock-pro_v1.1.0\data\回测结果\镜重圆策略\选股结果#0.镜重圆策略.pkl')
#%%
merge_df = pd.merge(zlm_df, df2[['交易日期', '股票代码', '总市值', '流通市值']], on=['交易日期', '股票代码'])
pd.options.display.float_format = '{:.2f}'.format

#%%
merge_df.describe()
#%%

import os
path = r'data/遍历结果'
dir_list = os.listdir(path)
# print(dir_list[1])
dir_list = [x for x in dir_list if '行业大票' in x]
# 读取所有CSV文件并合并
combined_data = []
for file in dir_list:
    # 读取CSV文件
    try:
        data = pd.read_csv(os.path.join(path, file, '策略评价.csv'), header=None, names=['指标', '值']).T.iloc[-1].tolist()
        data[0] = file
        print(data)
        # 将数据添加到合并字典中
        combined_data.append(data)
    except Exception as e:
        print(e)

# 将合并后的数据转换为DataFrame并转置
columns = '文件名,累积净值,年化收益,最大回撤,最大回撤开始时间,最大回撤结束时间,年化收益/回撤比,日盈亏,日盈比,周盈亏,周盈比,月盈亏,月盈比,最长未创新高天数,最长未创新高起始日,最长未创新高结束日,每日平均收益,盈亏收益比,单日最大盈利,单日最大亏损,最大连续盈利天数,最大连续亏损天数,收益率标准差,平均回撤面积,收益回撤指数,夏普比率'.split(',')
combined_df = pd.DataFrame(combined_data, columns=columns)

# 保存合并后的数据到新的CSV文件
combined_df.to_csv(os.path.join(path, '最优参数_行业大票_20250411.csv'), index=False, encoding='utf-8-sig')

print(combined_df)
# os.system(f'rmdir {dir_list[0]}')
#%%
pd.read_pickle(r'D:\projects\邢大小组\select-stock-pro_v1.1.0\data\遍历结果\S477-行业大票\config.pkl').strategy_list

#%%
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from itertools import combinations
import numpy as np
plt.rcParams['font.sans-serif'] = ['SimHei'] # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False # 用来正常显示负号

# 示例CSV文件路径列表
csv_files = ['成交额STD_筹码胜率', '瞎练0520', '行业大票']

# 读取所有CSV文件并提取相关列
dfs = []
for file in csv_files:
    df = pd.read_csv(f'D:\projects\邢大小组\select-stock-pro_v1.1.0\data\回测结果\\{file}\选股结果.csv')
    dfs.append(df)

# 为每条记录生成唯一键
def generate_unique_key(row):
    return f"{row['股票代码']}_{row['选股日期']}"

# 统计每条记录在不同文件中的出现次数
record_counts = {}
for i, df in enumerate(dfs):
    df['unique_key'] = df.apply(generate_unique_key, axis=1)
    for key in df['unique_key']:
        if key not in record_counts:
            record_counts[key] = [0] * len(dfs)
        record_counts[key][i] += 1

# 构建相关性矩阵
correlation_matrix = np.zeros((len(dfs), len(dfs)))

for i, j in combinations(range(len(dfs)), 2):
    # 计算每对文件之间的重复度相关性
    overlaps = []
    for key, counts in record_counts.items():
        if counts[i] > 0 and counts[j] > 0:
            overlaps.append(1)
        else:
            overlaps.append(0)
    corr = pd.Series(overlaps).corr(pd.Series(overlaps)) if overlaps else 0
    correlation_matrix[i, j] = corr
    correlation_matrix[j, i] = corr

# 将相关性矩阵转换为DataFrame
correlation_df = pd.DataFrame(correlation_matrix, index=csv_files, columns=csv_files)

# 绘制热力图
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_df, annot=True, cmap='coolwarm', vmin=0, vmax=1)
plt.title('文件间交易记录重复度热力图')
plt.show()

# 保存相关性矩阵到CSV文件
# correlation_df.to_csv('correlation_matrix.csv')
#%%
