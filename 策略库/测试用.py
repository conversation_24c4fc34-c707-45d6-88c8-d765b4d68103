
backtest_name = '测试用'
strategy_list = [
    {
        'name': backtest_name,
        'hold_period': '5',
        'offset_list': [2],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [
            ('窗口极值比', True, ('<PERSON>', 3), 1),
			('窗口极值比', True, ('Min', 5), 1),
			('窗口极值比', True, ('Min', 8), 1),
			('窗口极值比', True, ('Min', 13), 1),
			('窗口极值比', True, ('<PERSON>', 21), 1),
			('窗口极值比', True, ('<PERSON>', 30), 1),
			('窗口极值比', True, ('Min', 60), 1),
			('窗口极值比', True, ('Min', 120), 1),
			('窗口极值比', True, ('Min', 250), 1),
			('窗口极值比', True, ('<PERSON>', 3), 1),
			('窗口极值比', True, ('<PERSON>', 5), 1),
			('窗口极值比', True, ('<PERSON>', 8), 1),
			('窗口极值比', True, ('<PERSON>', 13), 1),
			('窗口极值比', True, ('<PERSON>', 21), 1),
			('窗口极值比', <PERSON>, ('<PERSON>', 30), 1),
			('窗口极值比', True, ('<PERSON>', 60), 1),
			('窗口极值比', True, ('<PERSON>', 120), 1),
			('窗口极值比', True, ('<PERSON>', 250), 1),
			('低位距离', True, (1, 3), 1),
			('低位距离', True, (2, 3), 1),
			('低位距离', True, (3, 3), 1),
			('低位距离', True, (4, 3), 1),
			('低位距离', True, (5, 3), 1),
			('低位距离', True, (1, 5), 1),
			('低位距离', True, (2, 5), 1),
			('低位距离', True, (3, 5), 1),
			('低位距离', True, (4, 5), 1),
			('低位距离', True, (5, 5), 1),
			('低位距离', True, (1, 8), 1),
			('低位距离', True, (2, 8), 1),
			('低位距离', True, (3, 8), 1),
			('低位距离', True, (4, 8), 1),
			('低位距离', True, (5, 8), 1),
			('低位距离', True, (1, 13), 1),
			('低位距离', True, (2, 13), 1),
			('低位距离', True, (3, 13), 1),
			('低位距离', True, (4, 13), 1),
			('低位距离', True, (5, 13), 1),
			('低位距离', True, (1, 21), 1),
			('低位距离', True, (2, 21), 1),
			('低位距离', True, (3, 21), 1),
			('低位距离', True, (4, 21), 1),
			('低位距离', True, (5, 21), 1),
			('低位距离', True, (1, 30), 1),
			('低位距离', True, (2, 30), 1),
			('低位距离', True, (3, 30), 1),
			('低位距离', True, (4, 30), 1),
			('低位距离', True, (5, 30), 1),
			('低位距离', True, (1, 60), 1),
			('低位距离', True, (2, 60), 1),
			('低位距离', True, (3, 60), 1),
			('低位距离', True, (4, 60), 1),
			('低位距离', True, (5, 60), 1),
			('低位距离', True, (1, 120), 1),
			('低位距离', True, (2, 120), 1),
			('低位距离', True, (3, 120), 1),
			('低位距离', True, (4, 120), 1),
			('低位距离', True, (5, 120), 1),
			('低位距离', True, (1, 250), 1),
			('低位距离', True, (2, 250), 1),
			('低位距离', True, (3, 250), 1),
			('低位距离', True, (4, 250), 1),
			('低位距离', True, (5, 250), 1)
        ],
        'filter_list': [
        ]
    }
]
    