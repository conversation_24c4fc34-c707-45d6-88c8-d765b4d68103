
backtest_name = '优化策略_v6'
strategy_list = [
    {
        'name': backtest_name,  # 策略名
        'hold_period': '3D',  # 持仓周期，W 代表周，M 代表月，还支持日频：3D、5D、10D
        'offset_list': [0, 1, 2],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        "factor_list": [  # 选股因子列表
            # ** 因子格式说明 **
            # 因子名称（与 '因子库' 文件中的名称一致），排序方式（True 为升序，False 为降序），因子参数，因子权重
            ('市值', True, None, 0.7),
            ('归母净利润同比增速', False, 60, 0.7),  # 利润增速高优先
            ('窗口极值比', True, ('Max', 60) , 0.5),
            # ('最大回撤', True, 60, 1.8),  # 60天回撤
            # ('Bias', True, 5, 0.2),  # 短期偏离因子
            # ('Bias', True, 10, 0.3),  # 长期偏离因子

            # # 可添加多个选股因子
        ],
        "filter_list": [
              ('窗口极值比', ('Min', 60), 'pct:>=0.2'), # 排除60天接近最低价区间（连跌）
              # ('市值', None, 'val:<=3000000000'),
              # ('交易所', None, 'val:<=3'),
              # ('月份', [1, 4], 'val:!=1'),  # 不在1,4月份选股

        ]  # 过滤因子列表
    }
]