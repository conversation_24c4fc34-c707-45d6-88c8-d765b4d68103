"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import pandas as pd
# from core.model.strategy_config import StrategyConfig
import numpy as np

"""
使用范例：
{
    'name': '小市值_周黎明',
    'hold_period': 'W',
    'offset_list': [0],
    'select_num': 5,
    'cap_weight': 1,
    'rebalance_time': 'open',
    'factor_list': [('Ret', False, 5, 100),
                    ('Ret', False, 20, 0.2),
                    ('一级行业', False, '', 2),
                    ('市值', True, '', 1),],
    'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
}
"""
backtest_name = '小市值_周黎明2'
strategy_list = [
    {
        'name': backtest_name,
        'hold_period': 'W',
        'offset_list': [0,1,2,3,4],
        'select_num': 3,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [
            # ('Ret', False, 5, 100),

            ('跌停起步改', False, (4, 21, 1.05), 200),
            ('Ret', False, 21, 0.2),
            ('一级行业', False, None, 1),
            ('市值', True, None, 1),
                        ],
        'filter_list': [
            # ('交易所', 'bj', "val:!=1"),
            # ('交易所', 'kcb', "val:!=1"),
            # ('成交额Mean', 5, 'val:>=5000_0000', True)
        ],

        'timing': {
            'name': '定风波1P5择时',  # 择时策略名称
            'limit': 0.8,
            'factor_list': [('开盘至今涨幅', False, '0945', 1, '0945'),
                            ('隔夜涨跌幅', False, '开盘价', 1, '开盘价'),
                            # ('开盘至今涨幅', False, None, 1, '0945'),
                            # ('隔夜涨跌幅', False, None, 1, '0935'),
                            # ('次日涨跌停状态', False, '涨停', 1, '0945'),
                            # ('次日涨跌停状态', False, '跌停', 1, '0945')
                            ],
            'params': 0.85
        }
    }
    # for m in [3, 5, 8, 13, 21, 30]
    # for n in range(1, 5)
    # for jys in ['cyb', 'kcb', 'bj', 'zb']
    # for p in [1.03, 1.05, 1.1]
]

'''
原策略请看帖子：https://bbs.quantclass.cn/thread/25799
'''

def calc_select_factor(df, strategy) -> pd.DataFrame:
    """
    计算复合选股因子
    :param df: 整理好的数据，包含因子信息，并做过周期转换
    :param strategy: 策略配置
    :return: 返回过滤后的数据

    ### df 列说明
    包含基础列：  ['交易日期', '股票代码', '股票名称', '周频起始日', '月频起始日', '上市至今交易天数', '复权因子', '开盘价', '最高价',
                '最低价', '收盘价', '成交额', '是否交易', '流通市值', '总市值', '下日_开盘涨停', '下日_是否ST', '下日_是否交易',
                '下日_是否退市']
    以及config中配置好的，因子计算的结果列。

    ### strategy 数据说明
    - strategy.name: 策略名称
    - strategy.hold_period: 持仓周期
    - strategy.select_num: 选股数量
    - strategy.factor_name: 复合因子名称
    - strategy.factor_list: 选股因子列表
    - strategy.filter_list: 过滤因子列表
    - strategy.factor_columns: 选股+过滤因子的列名
    """
    # 读取因子信息
    ret_short, ret_long, industy, mcap, *others = strategy.factor_list

    df = df[~df['股票代码'].str.contains('bj')]

    # 先计算 Ret_short 的排名
    df['Ret_short排名'] = df.groupby(['交易日期'])[ret_short.col_name].rank(ascending=False)

    # 计算一级行业个数
    df['一级行业个数'] = df.groupby(['交易日期', industy.col_name])['股票代码'].transform('count')

    # 对 Ret_short排名 和 一级行业个数进行复合排名
    # df['综合排名'] = df.groupby('交易日期').apply(lambda x: x['Ret_short排名'].rank(method='first', ascending=False) + x['一级行业个数'].rank(method='first', ascending=False)) \
    #     .reset_index(level=0, drop=True)

    df['排名靠前'] = np.where(df['Ret_short排名'] <= ret_short.weight, 1, 0)
    # df['排名靠前'] = np.where(df['综合排名'] <= ret_short.weight, 1, 0)

    ind_strength = df.groupby(['交易日期', industy.col_name])['排名靠前'].sum().reset_index()
    ind_strength['排名靠前_排名'] = ind_strength.groupby('交易日期')['排名靠前'].rank(ascending=False)

    ind_strength = ind_strength[ind_strength['排名靠前_排名'] <= industy.weight]

    df = pd.merge(df, ind_strength[['交易日期', industy.col_name]], on=['交易日期', industy.col_name])

    # 计算Ret20排名  衡量超跌
    df['Ret_long排名'] = df.groupby(['交易日期'])[ret_long.col_name].rank(ascending=False, method='min', pct=True)

    df = df[df['Ret_long排名'] > ret_long.weight]

    df['市值排名'] = df.groupby(['交易日期'])[mcap.col_name].rank(ascending=True, method='min')

    # 按照主力资金杠杆效率和市值排序
    df['复合因子'] = df['市值排名']

    df['复合因子百分位'] = df.groupby(['交易日期'])["复合因子"].rank(ascending=True, method='min', pct=True)

    return df

