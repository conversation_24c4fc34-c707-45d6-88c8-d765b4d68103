"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""

# ====================================================================================================
# 3️⃣ 策略配置
# ====================================================================================================
backtest_name = '北交所策略'
strategy_list = [
    {'name': '北交所策略',
     'hold_period': '2D',
     'offset_list': [0, 1],
     'select_num': 5,
     'cap_weight': 1,
     'rebalance_time': 'open',
     'factor_list': [
         ('市值', True, '', 2),
         ('Ret', True, 13, 1),
         ("最大回撤", True, 60, 1)
     ],
     'filter_list': [
         ('交易所', 'bj', 'val:==1'),
         ('alpha95', 5, 'pct:<=0.8'),
     ]

     }
]
