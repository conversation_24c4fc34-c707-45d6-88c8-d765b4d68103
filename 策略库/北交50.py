
import pandas as pd
from core.model.strategy_config import StrategyConfig


"""
使用范例：
{
    'name': '新股小市值',
    'hold_period': 'W',
    'offset_list': [0],
    'select_num': 5,
    'cap_weight': 1,
    'rebalance_time': 'open',
    'factor_list': [('市值', True, '', 100),
                    ('上市至今交易天数', True, '', 1), ],
    'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
}
"""


def filter_stock(df, strategy: StrategyConfig) -> pd.DataFrame:
    """
    过滤函数，在选股前过滤
    :param df: 整理好的数据，包含因子信息，并做过周期转换
    :param strategy: 策略配置
    :return: 返回过滤后的数据

    ### df 列说明
    包含基础列：  ['交易日期', '股票代码', '股票名称', '上市至今交易天数', '复权因子', '开盘价', '最高价',
                '最低价', '收盘价', '成交额', '是否交易', '流通市值', '总市值', '下日_开盘涨停', '下日_是否ST', '下日_是否交易',
                '下日_是否退市']
    以及config中配置好的，因子计算的结果列。

    ### strategy 数据说明
    - strategy.name: 策略名称
    - strategy.hold_period: 持仓周期
    - strategy.select_num: 选股数量
    - strategy.factor_name: 复合因子名称
    - strategy.factor_list: 选股因子列表
    - strategy.filter_list: 过滤因子列表
    - strategy.factor_columns: 选股+过滤因子的列名
    """
    cond = True
    # 删除月末为st状态的周期数
    cond &= ~df['股票名称'].str.contains('ST', regex=False)
    # 删除月末为s状态的周期数
    cond &= ~df['股票名称'].str.contains('S', regex=False)
    # 删除月末有退市风险的周期数
    cond &= ~df['股票名称'].str.contains('*', regex=False)
    cond &= ~df['股票名称'].str.contains('退', regex=False)
    # 删除交易天数过少的周期数
    # cond &= df['交易天数'] / df['市场交易天数'] >= 0.8

    cond &= df['下日_是否交易'] == 1
    cond &= df['下日_开盘涨停'] != 1
    cond &= df['下日_是否ST'] != 1
    cond &= df['下日_是否退市'] != 1
    cond &= df['上市至今交易天数'] > 60

    # common_filter = cond1 & cond2 & cond3 & cond4 & cond6 & cond7 & cond8 & cond9

    # common_filter = cond1 & cond2 & cond3 & cond4 & cond6 & cond7 & cond8 & cond9

    cond &= df['股票代码'].str.contains('bj') == True

    return df[cond]


# def calc_select_factor(df, strategy: StrategyConfig) -> pd.DataFrame:
#     """
#     计算复合选股因子
#     :param df: 整理好的数据，包含因子信息，并做过周期转换
#     :param strategy: 策略配置
#     :return: 返回过滤后的数据
#
#     ### df 列说明
#     包含基础列：  ['交易日期', '股票代码', '股票名称', '周频起始日', '月频起始日', '上市至今交易天数', '复权因子', '开盘价', '最高价',
#                 '最低价', '收盘价', '成交额', '是否交易', '流通市值', '总市值', '下日_开盘涨停', '下日_是否ST', '下日_是否交易',
#                 '下日_是否退市']
#     以及config中配置好的，因子计算的结果列。
#
#     ### strategy 数据说明
#     - strategy.name: 策略名称
#     - strategy.hold_period: 持仓周期
#     - strategy.select_num: 选股数量
#     - strategy.factor_name: 复合因子名称
#     - strategy.factor_list: 选股因子列表
#     - strategy.filter_list: 过滤因子列表
#     - strategy.factor_columns: 选股+过滤因子的列名
#     """
#
#     # 读取因子信息
#     mcap, vib = strategy.factor_list
#
#     # 读取参数
#     mcap_rank = mcap.args   # 最大市值排名
#
#     df = df[df['股票代码'].str.contains('sh68') == False]
#     df = df[df['股票代码'].str.contains('sz30') == False]
#
#     # ===构建策略需要的因子
#     df['总市值_排名'] = df.groupby('交易日期')[mcap.col_name].rank(ascending=mcap.is_sort_asc, method='min')
#     df = df[df['总市值_排名'] <= mcap_rank]
#
#     df['上市至今交易天数_排名'] = df.groupby('交易日期')[vib.col_name].rank(ascending=vib.is_sort_asc, method='min')
#
#     # === 计算复合因子
#     df['复合因子'] = df['上市至今交易天数_排名']
#
#     return df
