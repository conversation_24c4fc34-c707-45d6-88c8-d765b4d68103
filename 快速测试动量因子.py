#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试股价恢复速度动量因子

这个脚本可以快速测试动量因子是否正常工作
"""

def quick_test():
    """快速测试动量因子"""
    print("🚀 快速测试股价恢复速度动量因子")
    print("=" * 60)
    
    try:
        # 1. 测试因子导入
        print("1️⃣ 测试因子导入...")
        from core.utils.factor_hub import FactorHub
        
        factor_instance = FactorHub.get_by_name('跌停起步改')
        print("   ✅ 因子导入成功")
        
        # 2. 测试因子计算
        print("\n2️⃣ 测试因子计算...")
        import pandas as pd
        import numpy as np
        
        # 创建简单测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        test_data = pd.DataFrame({
            '交易日期': dates,
            '收盘价': 10 + np.cumsum(np.random.randn(100) * 0.01),
            '收盘价_复权': None,
            '跌停价': None,
        })
        
        test_data['收盘价_复权'] = test_data['收盘价']
        test_data['跌停价'] = test_data['收盘价'] * 0.9
        
        # 设置几个跌停
        test_data.loc[20, '收盘价'] = test_data.loc[20, '跌停价']
        test_data.loc[20, '收盘价_复权'] = test_data.loc[20, '跌停价']
        test_data.loc[50, '收盘价'] = test_data.loc[50, '跌停价']
        test_data.loc[50, '收盘价_复权'] = test_data.loc[50, '跌停价']
        
        # 测试不同方法
        test_params = [
            (1, 10, 1.05, "恢复天数动量"),
            (2, 10, 1.05, "恢复幅度动量"),
            (3, 10, 1.05, "恢复速度动量"),
            (4, 10, 1.05, "综合动量评分"),
        ]
        
        for param in test_params:
            method, lookback, threshold, name = param
            col_name = f'test_factor_{method}'
            
            try:
                result = factor_instance.add_factor(
                    test_data.copy(),
                    param=(method, lookback, threshold),
                    col_name=col_name
                )
                
                non_zero = (result[col_name] != 0).sum()
                max_val = result[col_name].max()
                min_val = result[col_name].min()
                
                print(f"   ✅ {name}: 非零值{non_zero}个, 范围[{min_val:.3f}, {max_val:.3f}]")
                
            except Exception as e:
                print(f"   ❌ {name}: 计算失败 - {e}")
        
        # 3. 测试因子分析工具
        print("\n3️⃣ 测试因子分析工具...")
        try:
            # 检查是否有因子分析工具
            import os
            if os.path.exists('因子分析工具.py'):
                print("   ✅ 因子分析工具存在")
                
                # 尝试导入分析函数
                namespace = {}
                with open('因子分析工具.py', 'r', encoding='utf-8') as f:
                    code = f.read()
                exec(code, namespace)
                
                validate_func = namespace.get('validate_and_analyze_factor')
                if validate_func:
                    print("   ✅ 分析函数导入成功")
                else:
                    print("   ⚠️ 分析函数未找到")
            else:
                print("   ⚠️ 因子分析工具不存在")
                
        except Exception as e:
            print(f"   ⚠️ 因子分析工具测试失败: {e}")
        
        # 4. 检查配置
        print("\n4️⃣ 检查配置...")
        try:
            import config as cfg
            print("   ✅ config.py 导入成功")
            
            if hasattr(cfg, 'runtime_folder'):
                print(f"   ✅ runtime_folder: {cfg.runtime_folder}")
            else:
                print("   ⚠️ runtime_folder 未配置")
                
        except ImportError:
            print("   ❌ config.py 导入失败")
        
        print("\n🎉 快速测试完成！")
        print("\n📋 下一步操作建议:")
        print("1. 在config.py中配置因子参数")
        print("2. 运行 program/step2_计算因子.py")
        print("3. 使用 因子分析工具.py 进行分析")
        print("4. 配置选股策略并运行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_config_example():
    """显示配置示例"""
    print("\n" + "=" * 60)
    print("⚙️ 配置示例")
    print("=" * 60)
    
    config_example = '''
# 在您的config.py中添加以下配置:

factor_params_dict = {
    '跌停起步改': [
        (1, 10, 1.05),   # 快速恢复-天数动量
        (2, 20, 1.05),   # 标准恢复-幅度动量
        (3, 30, 1.10),   # 长期恢复-速度动量
        (4, 20, 1.05),   # 综合动量评分
    ],
    # ... 其他因子配置
}

# 选股策略配置
select_stock_list = [
    {
        'name': '动量恢复策略',
        'factor_list': [
            ('跌停起步改', False, (4, 20, 1.05), 1.0),
        ],
        'select_num': 20,
    }
]
'''
    
    print(config_example)


def main():
    """主函数"""
    print("🧪 股价恢复速度动量因子快速测试")
    print("=" * 60)
    
    # 运行快速测试
    success = quick_test()
    
    if success:
        # 显示配置示例
        show_config_example()
        
        print("\n✅ 测试通过！因子可以正常使用。")
    else:
        print("\n❌ 测试失败！请检查因子文件和环境配置。")


if __name__ == '__main__':
    main()
