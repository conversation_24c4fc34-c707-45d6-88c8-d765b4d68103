#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股价恢复速度动量因子配置示例

在您的config.py文件中添加以下配置来使用动量因子
"""

# ================================ 因子配置示例 ================================

# 1. 基础因子参数配置
factor_params_dict = {
    # 股价恢复速度动量因子配置
    '跌停起步改': [
        # 方法1: 恢复天数动量
        (1, 10, 1.03),   # 10天内恢复3%的天数动量
        (1, 15, 1.05),   # 15天内恢复5%的天数动量
        (1, 20, 1.05),   # 20天内恢复5%的天数动量
        
        # 方法2: 恢复幅度动量
        (2, 15, 1.03),   # 15天内恢复3%的幅度动量
        (2, 20, 1.05),   # 20天内恢复5%的幅度动量
        (2, 25, 1.08),   # 25天内恢复8%的幅度动量
        
        # 方法3: 恢复速度动量
        (3, 20, 1.05),   # 20天内恢复5%的速度动量
        (3, 30, 1.10),   # 30天内恢复10%的速度动量
        
        # 方法4: 综合动量评分
        (4, 15, 1.05),   # 15天内恢复5%的综合评分
        (4, 20, 1.05),   # 20天内恢复5%的综合评分
        (4, 25, 1.08),   # 25天内恢复8%的综合评分
    ],
    
    # 可以同时配置其他因子
    '市值': [None],
    'RSI': [14, 21, 28],
    # ... 其他因子
}

# 2. 选股策略配置示例
select_stock_list = [
    {
        'name': '快速恢复动量策略',
        'factor_list': [
            ('跌停起步改', False, (1, 10, 1.03), 1.0),  # 使用快速恢复天数动量
        ],
        'select_num': 20,
        'filter_list': [
            ('市值', None, '>', 50),  # 过滤小市值股票
        ]
    },
    
    {
        'name': '综合恢复动量策略', 
        'factor_list': [
            ('跌停起步改', False, (4, 20, 1.05), 0.6),  # 综合动量评分，权重60%
            ('市值', True, None, 0.4),                   # 市值因子，权重40%
        ],
        'select_num': 30,
        'filter_list': [
            ('ST因子', None, '==', 0),  # 过滤ST股票
        ]
    },
    
    {
        'name': '多周期动量策略',
        'factor_list': [
            ('跌停起步改', False, (2, 15, 1.03), 0.4),  # 短期恢复幅度
            ('跌停起步改', False, (3, 30, 1.10), 0.6),  # 长期恢复速度
        ],
        'select_num': 25,
    }
]

# 3. 因子分析配置
factor_analysis_config = {
    '跌停起步改': {
        'period_offset': '5D_0',  # 分析周期
        'bins': 10,              # 分组数量
        'start_date': '2020-01-01',
        'end_date': '2024-12-31',
    }
}

# ================================ 使用说明 ================================

"""
📋 配置说明:

1. factor_params_dict: 因子参数字典
   - 键: 因子名称 (对应因子库中的文件名)
   - 值: 参数列表，每个参数对应一个因子计算

2. 动量因子参数格式: (method, lookback_days, recovery_threshold)
   - method: 计算方法 (1-4)
     * 1: 恢复天数动量
     * 2: 恢复幅度动量  
     * 3: 恢复速度动量
     * 4: 综合动量评分
   - lookback_days: 回看天数 (10-30推荐)
   - recovery_threshold: 恢复阈值 (1.03-1.10推荐)

3. 选股策略配置:
   - factor_list: 因子列表，格式为 (因子名, 是否升序, 参数, 权重)
   - select_num: 选股数量
   - filter_list: 过滤条件

4. 使用步骤:
   ① 将配置添加到您的config.py文件
   ② 运行 program/step2_计算因子.py 计算因子
   ③ 运行 program/step3_选股.py 进行选股
   ④ 使用 因子分析工具.py 进行因子分析

5. 参数优化建议:
   - 先使用默认参数测试
   - 通过因子分析工具找到最优参数
   - 根据市场环境调整参数
   - 定期重新优化

6. 性能考虑:
   - 参数过多会增加计算时间
   - 建议先测试少量参数
   - 确认效果后再扩展参数范围
"""
